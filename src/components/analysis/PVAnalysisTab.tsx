import React, { useState, useEffect } from 'react';
import { Table, Card, Checkbox, Button, Space, Row, Col, Select, Empty, Statistic, Divider, Modal, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { PVModule } from '../../types/project';
import { ProjectData, ProjectHourlyData, ProjectMonthlyData } from '../../types/projectData';
import { formatCurrency, formatNumber } from '../../utils';
import PVGenerationChart from './PVGenerationChart';
import PVModuleModal from './PVModuleModal';
import StatisticWithTooltip from '../../components/common/StatisticWithTooltip';

const { Option } = Select;

interface PVAnalysisTabProps {
  project: ProjectData;
  onAddModule?: () => void;
  onEditModule?: (module: PVModule) => void;
  onDeleteModule?: (moduleId: string) => void;
}

interface PVVisualizationOptions {
  metric: 'generation' | 'income';
  viewMode: 'hourly' | 'daily' | 'monthly';
  month: number;
  day: number;
}

/**
 * 光伏分析标签页组件
 */
const PVAnalysisTab: React.FC<PVAnalysisTabProps> = ({ project, onAddModule, onEditModule, onDeleteModule }) => {
  const { t } = useTranslation();
  const [selectedModules, setSelectedModules] = useState<string[]>([]);
  const [visualizationOptions, setVisualizationOptions] = useState<PVVisualizationOptions>({
    metric: 'generation',
    viewMode: 'monthly',
    month: 0, // 0表示全年
    day: 1
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [editingModule, setEditingModule] = useState<PVModule | null>(null);
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
  const [moduleToDelete, setModuleToDelete] = useState<string | null>(null);

  // 当项目变化时，重置选中的组件
  useEffect(() => {
    if (project.pvModules.length > 0) {
      // 默认选中第一个组件
      setSelectedModules([project.pvModules[0].id]);
    } else {
      setSelectedModules([]);
    }
  }, [project]);

  // 处理组件选择变更
  const handleModuleSelectionChange = (moduleId: string, checked: boolean) => {
    if (checked) {
      setSelectedModules(prev => [...prev, moduleId]);
    } else {
      setSelectedModules(prev => prev.filter(id => id !== moduleId));
    }
  };

  // 处理可视化选项变更
  const handleVisualizationOptionsChange = (options: Partial<PVVisualizationOptions>) => {
    setVisualizationOptions(prev => ({ ...prev, ...options }));
  };

  // 获取选中组件的数据
  const getSelectedModulesData = () => {
    console.log('PVAnalysisTab - 开始获取选中组件数据');
    console.log('PVAnalysisTab - 选中的组件ID:', selectedModules);

    const selectedModulesList = project.pvModules.filter(module =>
      selectedModules.includes(module.id)
    );
    console.log('PVAnalysisTab - 选中的组件数量:', selectedModulesList.length);

    if (selectedModulesList.length === 0) {
      console.warn('PVAnalysisTab - 没有选中任何组件');
      return null;
    }

    // 获取小时数据
    const hourlyData = project.analysisResults?.hourlyData || [];
    console.log('PVAnalysisTab - 小时数据点数量:', hourlyData.length);

    // 检查hourlyData的结构
    if (hourlyData.length > 0) {
      console.log('PVAnalysisTab - 小时数据样本:', hourlyData[0]);
      console.log('PVAnalysisTab - pvGeneration类型:', typeof hourlyData[0].pvGeneration);
      console.log('PVAnalysisTab - pvGeneration值:', hourlyData[0].pvGeneration);
    } else {
      console.warn('PVAnalysisTab - 没有小时数据');
    }

    // 每月的天数（非闰年）
    const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    // 初始化月数据结构
    const monthlyData: any[] = [];
    for (let month = 1; month <= 12; month++) {
      monthlyData.push({
        month,
        pvGeneration: 0,
        pvBenefit: 0
      });
    }
    console.log('PVAnalysisTab - 初始化月数据结构，数量:', monthlyData.length);

    // 初始化日数据结构
    const dailyData: any[] = [];
    for (let month = 1; month <= 12; month++) {
      for (let day = 1; day <= daysInMonth[month - 1]; day++) {
        dailyData.push({
          day,
          month,
          pvGeneration: 0,
          pvBenefit: 0
        });
      }
    }
    console.log('PVAnalysisTab - 初始化日数据结构，数量:', dailyData.length);

    // 初始化小时数据结构
    const mergedHourlyData: any[] = [];
    for (let month = 1; month <= 12; month++) {
      for (let day = 1; day <= daysInMonth[month - 1]; day++) {
        for (let hour = 0; hour < 24; hour++) {
          mergedHourlyData.push({
            hour,
            day,
            month,
            pvGeneration: 0,
            pvBenefit: 0
          });
        }
      }
    }
    console.log('PVAnalysisTab - 初始化小时数据结构，数量:', mergedHourlyData.length);

    // 处理每个小时数据
    console.log('PVAnalysisTab - 开始处理小时数据');
    let processedHours = 0;
    let totalSelectedGeneration = 0;
    let totalBenefit = 0;

    for (const hour of hourlyData) {
      // 获取当前小时的电价
      const price = hour.electricityPrice || 25; // 使用小时数据中的电价，如果没有则使用默认值
      const gridFeedInPrice = hour.gridFeedInPrice || 17; // 使用小时数据中的上网电价，如果没有则使用默认值

      // 获取选中组件在当前小时的发电量
      let selectedModulesGeneration = 0;
      for (const moduleId of selectedModules) {
        // 检查该组件在当前小时是否有发电数据
        if (hour.pvGeneration && hour.pvGeneration[moduleId] !== undefined) {
          const moduleGeneration = hour.pvGeneration[moduleId] || 0;
          selectedModulesGeneration += moduleGeneration;

          // 只在前几个小时记录详细日志
          if (processedHours < 3) {
            console.log(`PVAnalysisTab - 小时${hour.month}/${hour.day} ${hour.hour}时，组件${moduleId}发电量:`, moduleGeneration, 'kWh');
          }
        }
      }
      totalSelectedGeneration += selectedModulesGeneration;

      // 计算总发电量
      const totalPvGeneration = Object.values(hour.pvGeneration || {}).reduce((sum, val) => sum + (val as number || 0), 0);

      // 计算总收益
      let totalHourBenefit = 0;

      if (totalPvGeneration <= hour.electricityConsumption) {
        // 全部自用
        totalHourBenefit = totalPvGeneration * price;

        // 只在前几个小时记录详细日志
        if (processedHours < 3) {
          console.log(`PVAnalysisTab - 小时${hour.month}/${hour.day} ${hour.hour}时，全部自用，电价:${price}日元/kWh，总收益:${totalHourBenefit}日元`);
        }
      } else {
        // 部分自用，部分上网
        totalHourBenefit = hour.electricityConsumption * price +
                          (totalPvGeneration - hour.electricityConsumption) * gridFeedInPrice;

        // 只在前几个小时记录详细日志
        if (processedHours < 3) {
          console.log(`PVAnalysisTab - 小时${hour.month}/${hour.day} ${hour.hour}时，部分自用(${hour.electricityConsumption.toFixed(3)}kWh)，部分上网(${(totalPvGeneration - hour.electricityConsumption).toFixed(3)}kWh)`);
          console.log(`PVAnalysisTab - 电价:${price}日元/kWh，上网电价:${gridFeedInPrice}日元/kWh，总收益:${totalHourBenefit}日元`);
        }
      }

      // 按照发电量比例分配收益给选中的组件
      const benefit = totalPvGeneration > 0 ?
        (selectedModulesGeneration / totalPvGeneration) * totalHourBenefit : 0;

      // 只在前几个小时记录详细日志
      if (processedHours < 3) {
        console.log(`PVAnalysisTab - 小时${hour.month}/${hour.day} ${hour.hour}时，选中组件收益:${benefit}JPY，比例:${totalPvGeneration > 0 ? (selectedModulesGeneration / totalPvGeneration) : 0}`);
      }

      totalBenefit += benefit;

      // 更新小时数据
      const hourIndex = mergedHourlyData.findIndex(h =>
        h.month === hour.month && h.day === hour.day && h.hour === hour.hour
      );
      if (hourIndex >= 0) {
        mergedHourlyData[hourIndex].pvGeneration = selectedModulesGeneration;
        mergedHourlyData[hourIndex].pvBenefit = benefit;
      }

      // 更新日数据
      const dayIndex = dailyData.findIndex(d => d.month === hour.month && d.day === hour.day);
      if (dayIndex >= 0) {
        dailyData[dayIndex].pvGeneration += selectedModulesGeneration;
        dailyData[dayIndex].pvBenefit += benefit;
      }

      // 更新月数据
      const monthIndex = hour.month - 1;
      if (monthIndex >= 0 && monthIndex < 12) {
        monthlyData[monthIndex].pvGeneration += selectedModulesGeneration;
        monthlyData[monthIndex].pvBenefit += benefit;
      }

      processedHours++;
    }
    console.log('PVAnalysisTab - 处理完成的小时数:', processedHours);
    console.log('PVAnalysisTab - 选中组件总发电量(直接累加):', totalSelectedGeneration, 'kWh');
    console.log('PVAnalysisTab - 选中组件总收益(直接累加):', totalBenefit, 'JPY');

    // 计算总发电量和总收益
    const totalPvGeneration = monthlyData.reduce((sum, month) => sum + month.pvGeneration, 0);
    const totalPvBenefit = monthlyData.reduce((sum, month) => sum + month.pvBenefit, 0);

    console.log('PVAnalysisTab - 选中组件总发电量(从月数据累加):', totalPvGeneration, 'kWh');
    console.log('PVAnalysisTab - 选中组件总收益(从月数据累加):', totalPvBenefit, 'JPY');

    return {
      pvGeneration: totalPvGeneration,
      pvBenefit: totalPvBenefit,
      monthlyData,
      dailyData,
      hourlyData: mergedHourlyData
    };
  };

  // 处理添加组件
  const handleAddModule = () => {
    setEditingModule(null);
    setModalVisible(true);
  };

  // 处理编辑组件
  const handleEditModule = (module: PVModule) => {
    setEditingModule(module);
    setModalVisible(true);
  };

  // 处理删除组件
  const handleDeleteModule = (moduleId: string) => {
    setModuleToDelete(moduleId);
    setDeleteConfirmVisible(true);
  };

  // 确认删除组件
  const confirmDeleteModule = () => {
    if (moduleToDelete && onDeleteModule) {
      onDeleteModule(moduleToDelete);
      // 如果删除的组件在选中列表中，则从选中列表中移除
      if (selectedModules.includes(moduleToDelete)) {
        setSelectedModules(prev => prev.filter(id => id !== moduleToDelete));
      }
      message.success(t('pvModules.deleteSuccess'));
    }
    setDeleteConfirmVisible(false);
    setModuleToDelete(null);
  };

  // 保存组件
  const handleSaveModule = (module: PVModule) => {
    if (onEditModule) {
      onEditModule(module);
      message.success(editingModule ? t('pvModules.editSuccess') : t('pvModules.addSuccess'));
    }
    setModalVisible(false);
    setEditingModule(null);
  };

  // 渲染组件列表
  const renderModuleList = () => {
    const columns = [
      {
        title: '',
        key: 'selection',
        width: 50,
        render: (_: any, record: PVModule) => (
          <Checkbox
            checked={selectedModules.includes(record.id)}
            onChange={(e) => handleModuleSelectionChange(record.id, e.target.checked)}
          />
        ),
      },
      {
        title: t('pvModules.name'),
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: t('pvModules.manufacturer'),
        dataIndex: 'manufacturer',
        key: 'manufacturer',
      },
      {
        title: t('pvModules.model'),
        dataIndex: 'model',
        key: 'model',
      },
      {
        title: t('pvModules.power') + ' (W)',
        dataIndex: 'power',
        key: 'power',
      },
      {
        title: t('pvModules.efficiency') + ' (%)',
        dataIndex: 'efficiency',
        key: 'efficiency',
      },
      {
        title: t('pvModules.area') + ' (m²)',
        dataIndex: 'area',
        key: 'area',
        render: (value: number) => formatNumber(value, 1),
      },
      {
        title: t('pvModules.quantity'),
        dataIndex: 'quantity',
        key: 'quantity',
      },
      {
        title: t('pvModules.totalPower') + ' (kW)',
        key: 'totalPower',
        render: (_: any, record: PVModule) => formatNumber((record.power * record.quantity) / 1000, 1),
      },
      {
        title: t('pvModules.totalArea') + ' (m²)',
        key: 'totalArea',
        render: (_: any, record: PVModule) => formatNumber(record.area * record.quantity, 1),
      },
      {
        title: t('common.operation'),
        key: 'operation',
        width: 120,
        render: (_: any, record: PVModule) => (
          <Space size="small">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditModule(record)}
              title={t('common.edit')}
            />
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteModule(record.id)}
              title={t('common.delete')}
            />
          </Space>
        ),
      },
    ];

    return (
      <Card
        title={t('pvModules.moduleList')}
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddModule}
          >
            {t('pvModules.addModule')}
          </Button>
        }
        style={{ marginBottom: 16 }}
      >
        <Table
          rowKey="id"
          columns={columns}
          dataSource={project.pvModules}
          pagination={false}
          size="small"
        />
      </Card>
    );
  };

  // 渲染可视化选项
  const renderVisualizationOptions = () => {
    const { metric, viewMode, month, day } = visualizationOptions;

    return (
      <div style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: '16px', marginBottom: 16 }}>
          {/* 数据指标 */}
          <div>
            <span style={{ marginRight: 8 }}>{t('chart.metric')}:</span>
            <Select
              value={metric}
              style={{ width: 120 }}
              onChange={(value) => handleVisualizationOptionsChange({ metric: value })}
            >
              <Option value="generation">{t('chart.generation')}</Option>
              <Option value="income">{t('chart.income')}</Option>
            </Select>
          </div>

          {/* 查看方式 */}
          <div>
            <span style={{ marginRight: 8 }}>{t('chart.viewMode')}:</span>
            <Select
              value={viewMode}
              style={{ width: 120 }}
              onChange={(value) => handleVisualizationOptionsChange({ viewMode: value })}
            >
              <Option value="hourly">{t('chart.hourly')}</Option>
              <Option value="daily">{t('chart.daily')}</Option>
              <Option value="monthly">{t('chart.monthly')}</Option>
            </Select>
          </div>

          {/* 月份选择 - 仅在非月视图时显示 */}
          {viewMode !== 'monthly' && (
            <div>
              <span style={{ marginRight: 8 }}>{t('chart.month')}:</span>
              <Select
                value={month}
                style={{ width: 80 }}
                onChange={(value) => handleVisualizationOptionsChange({ month: value })}
              >
                <Option key={0} value={0}>{t('chart.allYear')}</Option>
                {Array.from({ length: 12 }, (_, i) => i + 1).map(m => (
                  <Option key={m} value={m}>{m}{t('chart.monthUnit')}</Option>
                ))}
              </Select>
            </div>
          )}

          {/* 日期选择 - 仅在小时视图时显示 */}
          {viewMode === 'hourly' && month > 0 && (
            <div>
              <span style={{ marginRight: 8 }}>{t('chart.day')}:</span>
              <Select
                value={day}
                style={{ width: 80 }}
                onChange={(value) => handleVisualizationOptionsChange({ day: value })}
              >
                {Array.from({ length: 31 }, (_, i) => i + 1).map(d => (
                  <Option key={d} value={d}>{d}{t('chart.dayUnit')}</Option>
                ))}
              </Select>
            </div>
          )}
        </div>
      </div>
    );
  };

  // 渲染数据统计
  const renderDataStatistics = () => {
    const selectedData = getSelectedModulesData();
    if (!selectedData) return null;

    const { pvGeneration, pvBenefit } = selectedData;

    // 计算选中组件的总功率
    const selectedModulesList = project.pvModules.filter(module =>
      selectedModules.includes(module.id)
    );
    const totalPower = selectedModulesList.reduce(
      (sum, module) => sum + module.power * module.quantity, 0
    ) / 1000; // 转换为kW

    // 计算每kW的发电量和收益
    const generationPerKW = totalPower > 0 ? pvGeneration / totalPower : 0;
    const benefitPerKW = totalPower > 0 ? pvBenefit / totalPower : 0;

    // 计算总投资成本
    // 计算选中组件的组件成本
    const selectedModulesCost = selectedModulesList.reduce((sum, module) =>
      sum + module.price * module.quantity, 0);

    // 计算所有光伏组件的总功率
    const totalPVPower = project.pvModules.reduce(
      (sum, module) => sum + module.power * module.quantity, 0
    ) / 1000; // 转换为kW

    // 计算逆变器和其他投资总成本
    const invertersCost = project.inverters.reduce((sum, inverter) =>
      sum + inverter.price * inverter.quantity, 0);
    const otherInvestmentsCost = project.otherInvestments.reduce((sum, item) =>
      sum + item.price, 0);

    // 计算选中组件应分担的逆变器和其他投资成本（按装机功率比例分配）
    const powerRatio = totalPVPower > 0 ? totalPower / totalPVPower : 0;
    const allocatedInvertersCost = invertersCost * powerRatio;
    const allocatedOtherCost = otherInvestmentsCost * powerRatio;

    // 计算光伏年收益率 = 本组光伏的年收益 / (本组光伏的组件成本 + 本组光伏的装机功率/光伏装机总功率 * （逆变器投资 + 其他投资））
    const pvInvestment = selectedModulesCost + allocatedInvertersCost + allocatedOtherCost;
    const pvReturnRate = pvInvestment > 0 ? (pvBenefit / pvInvestment) * 100 : 0;

    console.log('PVAnalysisTab - 选中组件成本:', selectedModulesCost, 'JPY');
    console.log('PVAnalysisTab - 选中组件功率比例:', powerRatio);
    console.log('PVAnalysisTab - 分配的逆变器成本:', allocatedInvertersCost, 'JPY');
    console.log('PVAnalysisTab - 分配的其他投资成本:', allocatedOtherCost, 'JPY');
    console.log('PVAnalysisTab - 选中组件总投资:', pvInvestment, 'JPY');

    return (
      <Card title={t('analysis.pvDataStatistics')}>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.annualPVGeneration')}
              tooltip={t('analysis.tooltips.annualPVGeneration')}
              value={pvGeneration}
              precision={1}
              suffix="kWh"
            />
          </Col>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.annualPVBenefit')}
              tooltip={t('analysis.tooltips.annualPVBenefit')}
              value={pvBenefit}
              precision={1}
              prefix="JPY "
            />
          </Col>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.generationPerKW')}
              tooltip={t('analysis.tooltips.generationPerKW')}
              value={generationPerKW}
              precision={1}
              suffix="kWh/kW"
            />
          </Col>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.benefitPerKW')}
              tooltip={t('analysis.tooltips.benefitPerKW')}
              value={benefitPerKW}
              precision={1}
              prefix="JPY "
              suffix="/kW"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={6}>
            <StatisticWithTooltip
              title={t('analysis.pvReturnRate')}
              tooltip={t('analysis.tooltips.pvReturnRate')}
              value={pvReturnRate}
              precision={1}
              suffix="%"
            />
          </Col>
        </Row>
      </Card>
    );
  };

  return (
    <div>
      {renderModuleList()}

      {project.pvModules.length > 0 ? (
        <>
          {renderDataStatistics()}
          <Divider />
          {renderVisualizationOptions()}
          <PVGenerationChart
            data={getSelectedModulesData()}
            options={visualizationOptions}
          />
        </>
      ) : (
        <Empty
          description={t('pvModules.noModules')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )}

      {/* 添加/编辑组件模态框 */}
      <PVModuleModal
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onSave={handleSaveModule}
        editingModule={editingModule}
      />

      {/* 删除确认对话框 */}
      <Modal
        title={t('pvModules.confirmDelete')}
        open={deleteConfirmVisible}
        onOk={confirmDeleteModule}
        onCancel={() => setDeleteConfirmVisible(false)}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
      >
        <p>{t('pvModules.deleteConfirmMessage')}</p>
      </Modal>
    </div>
  );
};

export default PVAnalysisTab;
