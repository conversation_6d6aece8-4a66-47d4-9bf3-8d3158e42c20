import React, { useState, useEffect } from 'react';
import { Card, Empty, Alert, Checkbox, Space, Row, Col, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import { EChartsOption } from 'echarts';
import { TiltAnalysisData, TiltAnalysisResult } from '../../services/pvTiltAnalysisService';
import { formatNumber } from '../../utils';

// 定义图表指标类型
export type TiltMetric =
  | 'annualGeneration'      // 年发电量
  | 'annualBenefit'         // 年光伏收益
  | 'generationPerKW'       // 每千瓦发电量
  | 'benefitPerKW'          // 每千瓦收益
  | 'pvReturnRate';         // 光伏年收益率

interface PVTiltAnalysisChartProps {
  data: TiltAnalysisData | null;
  loading?: boolean;
  error?: string | null;
}

/**
 * 光伏倾角分析图表组件
 */
const PVTiltAnalysisChart: React.FC<PVTiltAnalysisChartProps> = ({
  data,
  loading = false,
  error = null
}) => {
  const { t } = useTranslation();
  const [selectedMetrics, setSelectedMetrics] = useState<TiltMetric[]>(['annualGeneration', 'annualBenefit']);
  const [chartOptions, setChartOptions] = useState<EChartsOption | null>(null);
  const [chartKey, setChartKey] = useState<number>(0);

  // 指标配置
  const metricConfig = {
    annualGeneration: {
      name: t('analysis.annualGenerationByTilt'),
      unit: 'kWh',
      color: '#1890ff',
      yAxisIndex: 0
    },
    annualBenefit: {
      name: t('analysis.annualBenefitByTilt'),
      unit: 'JPY',
      color: '#52c41a',
      yAxisIndex: 1
    },
    generationPerKW: {
      name: t('analysis.generationPerKWByTilt'),
      unit: 'kWh/kW',
      color: '#faad14',
      yAxisIndex: 0
    },
    benefitPerKW: {
      name: t('analysis.benefitPerKWByTilt'),
      unit: 'JPY/kW',
      color: '#f5222d',
      yAxisIndex: 1
    },
    pvReturnRate: {
      name: t('analysis.pvReturnRateByTilt'),
      unit: '%',
      color: '#722ed1',
      yAxisIndex: 2
    }
  };

  // 生成图表配置
  const generateChartOptions = (analysisData: TiltAnalysisData, metrics: TiltMetric[]) => {
    if (!analysisData || !analysisData.results || analysisData.results.length === 0) {
      return null;
    }

    const results = analysisData.results;
    const tiltAngles = results.map(r => r.tiltAngle);

    // 构建系列数据
    const series: any[] = [];
    const yAxes: any[] = [];

    // 根据选中的指标构建Y轴
    const usedYAxisIndices = new Set<number>();
    metrics.forEach(metric => {
      const config = metricConfig[metric];
      usedYAxisIndices.add(config.yAxisIndex);
    });

    // 创建Y轴配置
    Array.from(usedYAxisIndices).sort().forEach((yAxisIndex, index) => {
      let axisName = '';
      let axisUnit = '';
      
      if (yAxisIndex === 0) {
        axisName = 'kWh';
        axisUnit = 'kWh';
      } else if (yAxisIndex === 1) {
        axisName = 'JPY';
        axisUnit = 'JPY';
      } else if (yAxisIndex === 2) {
        axisName = '%';
        axisUnit = '%';
      }

      yAxes.push({
        type: 'value',
        name: axisName,
        position: index === 0 ? 'left' : index === 1 ? 'right' : 'right',
        offset: index > 1 ? (index - 1) * 80 : 0,
        axisLabel: {
          formatter: `{value} ${axisUnit}`
        },
        splitLine: {
          show: index === 0
        }
      });
    });

    // 构建系列数据
    metrics.forEach(metric => {
      const config = metricConfig[metric];
      const data = results.map(r => r[metric]);
      
      // 找到实际的Y轴索引
      const actualYAxisIndex = Array.from(usedYAxisIndices).sort().indexOf(config.yAxisIndex);

      series.push({
        name: config.name,
        type: 'line',
        data: data,
        yAxisIndex: actualYAxisIndex,
        lineStyle: {
          color: config.color,
          width: 2
        },
        itemStyle: {
          color: config.color
        },
        symbol: 'circle',
        symbolSize: 4,
        smooth: true
      });
    });

    const options: EChartsOption = {
      title: {
        text: t('analysis.tiltAnalysisTitle'),
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          if (!Array.isArray(params) || params.length === 0) return '';
          
          const tiltAngle = params[0].name;
          let tooltip = `${t('analysis.tiltAngle')}: ${tiltAngle}°<br/>`;
          
          params.forEach((param: any) => {
            const metric = metrics.find(m => metricConfig[m].name === param.seriesName);
            if (metric) {
              const config = metricConfig[metric];
              tooltip += `${param.seriesName}: ${formatNumber(param.value, 1)} ${config.unit}<br/>`;
            }
          });
          
          return tooltip;
        }
      },
      legend: {
        data: metrics.map(metric => metricConfig[metric].name),
        top: 30,
        type: 'scroll'
      },
      grid: {
        left: '10%',
        right: yAxes.length > 1 ? '15%' : '10%',
        bottom: '10%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: tiltAngles,
        name: `${t('analysis.tiltAngle')} (°)`,
        nameLocation: 'middle',
        nameGap: 30,
        axisLabel: {
          interval: 9, // 每10度显示一个标签
          formatter: '{value}°'
        }
      },
      yAxis: yAxes,
      series: series,
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: 0,
          start: 0,
          end: 100
        },
        {
          type: 'slider',
          xAxisIndex: 0,
          start: 0,
          end: 100,
          height: 20,
          bottom: 10
        }
      ]
    };

    return options;
  };

  // 当数据或选中指标变化时更新图表
  useEffect(() => {
    if (data && selectedMetrics.length > 0) {
      const options = generateChartOptions(data, selectedMetrics);
      setChartOptions(options);
      setChartKey(prev => prev + 1);
    }
  }, [data, selectedMetrics, t]);

  // 处理指标选择变化
  const handleMetricChange = (checkedValues: TiltMetric[]) => {
    setSelectedMetrics(checkedValues);
  };

  // 渲染指标选择器
  const renderMetricSelector = () => {
    const metricOptions = Object.keys(metricConfig).map(key => ({
      label: metricConfig[key as TiltMetric].name,
      value: key as TiltMetric
    }));

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row align="middle">
          <Col span={4}>
            <strong>{t('chart.metrics')}:</strong>
          </Col>
          <Col span={20}>
            <Checkbox.Group
              options={metricOptions}
              value={selectedMetrics}
              onChange={handleMetricChange}
            />
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染最优角度信息
  const renderOptimalInfo = () => {
    if (!data) return null;

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <strong>{t('analysis.optimalTiltAngle')}: </strong>
            <span style={{ color: '#1890ff', fontSize: '16px', fontWeight: 'bold' }}>
              {data.optimalTiltAngle}°
            </span>
          </Col>
          <Col span={8}>
            <strong>{t('analysis.annualGenerationByTilt')}: </strong>
            <span>{formatNumber(data.maxAnnualGeneration, 1)} kWh</span>
          </Col>
          <Col span={8}>
            <strong>{t('analysis.annualBenefitByTilt')}: </strong>
            <span>{formatNumber(data.maxAnnualBenefit, 1)} JPY</span>
          </Col>
        </Row>
      </Card>
    );
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>{t('analysis.tiltAnalysisCalculating')}</div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert
          message={t('analysis.tiltAnalysisError')}
          description={error}
          type="error"
          showIcon
        />
      </Card>
    );
  }

  if (!data || !data.results || data.results.length === 0) {
    return (
      <Card>
        <Empty
          description={t('analysis.noData')}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  return (
    <div>
      {renderOptimalInfo()}
      {renderMetricSelector()}
      <Card>
        {chartOptions && (
          <ReactECharts
            key={chartKey}
            option={chartOptions}
            style={{ height: '500px', width: '100%' }}
            opts={{ renderer: 'canvas' }}
          />
        )}
      </Card>
    </div>
  );
};

export default PVTiltAnalysisChart;
