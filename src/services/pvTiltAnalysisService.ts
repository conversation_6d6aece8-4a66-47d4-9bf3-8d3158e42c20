import { PVModule } from '../types/project';
import { ProjectData, ProjectHourlyData } from '../types/projectData';
import { IrradianceHourlyData } from '../types/database';
import { getIrradianceDetail } from './irradianceService';

/**
 * 倾角分析结果数据结构
 */
export interface TiltAnalysisResult {
  tiltAngle: number;                    // 倾角（度）
  annualGeneration: number;             // 年发电量 (kWh)
  annualBenefit: number;                // 年光伏收益 (JPY)
  generationPerKW: number;              // 每千瓦发电量 (kWh/kW)
  benefitPerKW: number;                 // 每千瓦收益 (JPY/kW)
  pvReturnRate: number;                 // 光伏年收益率 (%)
}

/**
 * 倾角分析完整结果
 */
export interface TiltAnalysisData {
  results: TiltAnalysisResult[];        // 0-90度每度的分析结果
  optimalTiltAngle: number;             // 最优倾角
  maxAnnualGeneration: number;          // 最大年发电量
  maxAnnualBenefit: number;             // 最大年收益
}

/**
 * 角度转弧度
 */
const degreesToRadians = (degrees: number): number => {
  return degrees * Math.PI / 180;
};

/**
 * 朝向转换为角度（与正南的夹角）
 */
const orientationToAngle = (orientation: string): number => {
  const orientationMap: { [key: string]: number } = {
    'south': 0,      // 正南
    'southeast': -45, // 东南
    'east': -90,     // 正东
    'northeast': -135, // 东北
    'north': 180,    // 正北
    'northwest': 135, // 西北
    'west': 90,      // 正西
    'southwest': 45,  // 西南
  };
  return orientationMap[orientation] || 0;
};

/**
 * 计算指定倾角下的光伏发电量
 */
const calculatePVGenerationForTilt = async (
  project: ProjectData,
  selectedModules: PVModule[],
  tiltAngle: number
): Promise<{ generation: number; benefit: number }> => {
  try {
    // 获取光照数据
    const irradianceData = await getIrradianceDetail(project.irradianceDataId);
    if (!irradianceData || !irradianceData.hourlyData) {
      throw new Error('无法获取光照数据');
    }

    let totalGeneration = 0;
    let totalBenefit = 0;

    // 逆变器效率（假设为95%）
    const inverterEfficiency = 0.95;

    // 遍历每小时数据
    for (const irradianceHour of irradianceData.hourlyData) {
      let hourlyGeneration = 0;

      // 遍历选中的光伏组件
      for (const module of selectedModules) {
        // 计算组件效率（百分比转为小数）
        const efficiency = module.efficiency / 100;

        // 获取朝向角（与正南的夹角）
        const orientationAngle = orientationToAngle(module.orientation);

        // 获取太阳高度角和方位角
        const sunHeight = irradianceHour.sunHeight;
        const sunAngle = irradianceHour.sunAngle;

        // 计算辐射量
        const diffuseRadiation = irradianceHour.diffuseHorizontalIrradiance;

        // 直射辐射计算
        const sunHeightRad = degreesToRadians(sunHeight);
        const installAngleRad = degreesToRadians(tiltAngle); // 使用指定的倾角
        const sunAngleRad = degreesToRadians(sunAngle);
        const orientationAngleRad = degreesToRadians(orientationAngle);

        let directRadiation = 0;
        if (sunHeight > 0) {
          // 计算太阳光线与光伏板法线之间的夹角余弦值
          const cosTheta = Math.sin(sunHeightRad) * Math.cos(installAngleRad) +
                          Math.cos(sunHeightRad) * Math.sin(installAngleRad) * Math.cos(sunAngleRad - orientationAngleRad);

          if (cosTheta > 0) {
            directRadiation = irradianceHour.directNormalIrradiance * cosTheta;
          }
        }

        // 总辐射量 (W/m²)
        const totalRadiation = diffuseRadiation + Math.max(0, directRadiation);

        // 计算该组件的发电量 (kWh)
        const moduleArea = module.area * module.quantity;
        const moduleGeneration = (totalRadiation * moduleArea * efficiency * inverterEfficiency) / 1000;

        hourlyGeneration += moduleGeneration;
      }

      totalGeneration += hourlyGeneration;

      // 计算收益（从项目分析结果中获取电价信息）
      const hourlyData = project.analysisResults?.hourlyData;
      let electricityConsumption = 0;
      let electricityPrice = 25; // 默认电价 JPY/kWh
      let gridFeedInPrice = 17; // 默认上网电价 JPY/kWh

      if (hourlyData) {
        const matchingHour = hourlyData.find(h =>
          h.month === irradianceHour.month &&
          h.day === irradianceHour.day &&
          h.hour === irradianceHour.hour
        );
        if (matchingHour) {
          electricityConsumption = matchingHour.electricityConsumption;
          // 使用项目分析结果中的电价
          electricityPrice = matchingHour.electricityPrice || electricityPrice;
          gridFeedInPrice = matchingHour.gridFeedInPrice || gridFeedInPrice;
        }
      }

      // 计算该小时的收益
      let hourlyBenefit = 0;
      if (hourlyGeneration <= electricityConsumption) {
        // 全部自用
        hourlyBenefit = hourlyGeneration * electricityPrice;
      } else {
        // 部分自用，部分上网
        hourlyBenefit = electricityConsumption * electricityPrice +
                       (hourlyGeneration - electricityConsumption) * gridFeedInPrice;
      }

      totalBenefit += hourlyBenefit;
    }

    return {
      generation: parseFloat(totalGeneration.toFixed(3)),
      benefit: parseFloat(totalBenefit.toFixed(1))
    };
  } catch (error) {
    console.error('计算倾角发电量时出错:', error);
    throw error;
  }
};

/**
 * 执行光伏倾角分析
 */
export const performTiltAnalysis = async (
  project: ProjectData,
  selectedModuleIds: string[]
): Promise<TiltAnalysisData> => {
  try {
    console.log('开始倾角分析，选中组件:', selectedModuleIds);

    // 获取选中的组件
    const selectedModules = project.pvModules.filter(module =>
      selectedModuleIds.includes(module.id)
    );

    if (selectedModules.length === 0) {
      throw new Error('未选择任何组件');
    }

    // 计算总功率和总投资
    const totalPower = selectedModules.reduce((sum, module) =>
      sum + module.power * module.quantity, 0) / 1000; // 转换为kW

    const totalInvestment = selectedModules.reduce((sum, module) =>
      sum + module.price * module.quantity, 0);

    const results: TiltAnalysisResult[] = [];
    let maxGeneration = 0;
    let maxBenefit = 0;
    let optimalAngle = 0;

    // 对0-90度每1度进行计算
    for (let angle = 0; angle <= 90; angle++) {
      console.log(`计算倾角 ${angle} 度...`);

      const { generation, benefit } = await calculatePVGenerationForTilt(
        project,
        selectedModules,
        angle
      );

      const generationPerKW = totalPower > 0 ? generation / totalPower : 0;
      const benefitPerKW = totalPower > 0 ? benefit / totalPower : 0;
      const pvReturnRate = totalInvestment > 0 ? (benefit / totalInvestment) * 100 : 0;

      const result: TiltAnalysisResult = {
        tiltAngle: angle,
        annualGeneration: generation,
        annualBenefit: benefit,
        generationPerKW: parseFloat(generationPerKW.toFixed(1)),
        benefitPerKW: parseFloat(benefitPerKW.toFixed(1)),
        pvReturnRate: parseFloat(pvReturnRate.toFixed(1))
      };

      results.push(result);

      // 记录最大值和最优角度
      if (generation > maxGeneration) {
        maxGeneration = generation;
        optimalAngle = angle;
      }
      if (benefit > maxBenefit) {
        maxBenefit = benefit;
      }
    }

    console.log('倾角分析完成，最优角度:', optimalAngle);

    return {
      results,
      optimalTiltAngle: optimalAngle,
      maxAnnualGeneration: maxGeneration,
      maxAnnualBenefit: maxBenefit
    };
  } catch (error) {
    console.error('倾角分析失败:', error);
    throw error;
  }
};
