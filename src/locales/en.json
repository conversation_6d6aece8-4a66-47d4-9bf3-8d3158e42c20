{"app": {"title": "PV+Storage Project Economic Analysis", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "username": "Username", "password": "Password", "email": "Email"}, "nav": {"projects": "Projects", "databases": "Databases", "settings": "Settings", "irradiance": "Irradiance Database", "electricityPrice": "Electricity Price Database", "suppliers": "Suppliers Database", "equipment": "Equipment Database"}, "projects": {"title": "Projects", "new": "New Project", "edit": "Edit Project", "delete": "Delete Project", "search": "Search Projects", "filter": "Filter", "name": "Project Name", "location": "Location", "capacity": "Capacity", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "draft": "Draft", "analyzing": "Analyzing", "completed": "Completed", "saveDraft": "Save Draft", "startAnalysis": "Start Analysis", "analysisResults": "Analysis Results", "confirmDelete": "Confirm delete project", "deleteConfirmation": "The project cannot be recovered after deletion. Confirm delete?", "deleted": "Project deleted", "notFound": "Project not found", "updated": "Project updated", "nameRequired": "Please enter project name", "namePlaceholder": "Please enter project name", "capacityRequired": "Please enter project capacity", "locationRequired": "Please select project location", "selectLocation": "Please select province/city", "province": "Province", "city": "City", "district": "District/County", "town": "Town", "village": "Village", "districtRequired": "Please select district/county", "installationTypeProjectType": {"rooftop-residential": "Rooftop - Residential Project", "rooftop-commercial": "Rooftop - Commercial Project", "rooftop-industrial": "Rooftop - Industrial Project", "ground-utility": "Ground - Utility Project", "ground-commercial": "Ground - Commercial Project", "floating-utility": "Floating - Utility Project", "building-commercial": "Building-integrated - Commercial Project"}, "selectDistrict": "Please select district/county", "region": "Region", "area": "Area", "prefecture": "Prefecture", "prefectureUnit": "Prefecture Unit", "regionRequired": "Please select region", "selectRegion": "Please select region", "prefectureRequired": "Please select prefecture", "selectPrefecture": "Please select prefecture", "detailedAddress": "Detailed Address", "detailedAddressPlaceholder": "Please enter detailed address", "postalCode": "Postal Code", "postalCodePlaceholder": "Please enter postal code", "installationType": "Installation Type", "installationTypeRequired": "Please select installation type", "selectInstallationType": "Please select installation type", "projectType": "Project Type", "projectTypeRequired": "Please select project type", "selectProjectType": "Please select project type", "connectionType": "Connection Type", "connectionTypeRequired": "Please select connection type", "selectConnectionType": "Please select connection type", "estimatedPower": "Estimated Power", "estimatedCapacity": "Estimated Capacity", "estimatedInvestment": "Estimated Investment", "description": "Project Description", "descriptionPlaceholder": "Please enter project description", "manager": "Project Manager", "managerPlaceholder": "Please enter project manager", "created": "Project created successfully", "draftSaved": "Draft saved successfully", "draftSavedLocally": "Draft saved locally (server unavailable)", "syncToServer": "Sync to server", "syncing": "Syncing...", "syncSuccess": "Sync successful", "syncFailed": "Sync failed", "deleteFailed": "Failed to delete project", "fetchFailed": "Failed to fetch project list", "analysisCompleted": "Project analysis completed", "analysisFailed": "Project analysis failed", "exportHourlyData": "Export project hourly data", "exportSuccess": "Export successful", "exportFailed": "Export failed"}, "projectWizard": {"step1": "Basic Information", "step2": "Irradiance Data", "step3": "Electricity Price", "step4": "Electricity Usage", "step5": "PV Modules", "step6": "Energy Storage", "step7": "Inverters & Other Equipment", "step8": "Other Investments", "next": "Next", "previous": "Previous", "required": "Required", "optional": "Optional", "selectExisting": "Select Existing", "createNew": "Create New", "basicInfoDescription": "Please fill in the basic information of the project, fields with * are required", "irradianceDataDescription": "Please select irradiance data for the project location, this will affect power generation calculation", "electricityPriceDescription": "Please select applicable electricity price policy, this will affect economic benefit calculation", "electricityUsageDescription": "Please input electricity usage data, you can input by hour, day or month", "pvModulesDescription": "Please configure PV modules, you can add multiple types of modules", "energyStorageDescription": "Please configure energy storage devices, you can skip this step if the project does not include energy storage", "invertersDescription": "Please configure inverters, each PV system must have at least one inverter", "otherInvestmentsDescription": "Please add other investment items, such as installation costs, design costs, etc.", "selectionRequired": "Please select an item", "irradianceSelectionRequired": "Please select an irradiance dataset", "electricityPriceSelectionRequired": "Please select an electricity price policy", "dataRequired": "Please input data", "electricityUsageDataRequired": "Please input electricity usage data", "pvModulesRequired": "Please add at least one PV module", "invertersRequired": "Please add at least one inverter", "allRequiredFieldsCompleted": "All required fields completed", "someRequiredFieldsIncomplete": "Some required fields are incomplete, please check fields marked with *", "atLeastOneEquipmentRequired": "Please add at least one type of equipment (PV modules, energy storage or inverters)", "incompleteStepWarning": "There are required fields not completed in this step", "incompleteFields": "Incomplete items"}, "analysis": {"title": "Analysis Results", "overview": "Overview", "pvAnalysis": "PV Analysis", "storageAnalysis": "Storage Analysis", "hourlyData": "Hourly Data", "dailyData": "Daily Data", "monthlyData": "Monthly Data", "yearlyData": "Yearly Data", "dataStatistics": "Data Statistics", "pvDataStatistics": "PV Data Statistics", "storageStatistics": "Storage Statistics", "pvGeneration": "PV Generation", "storageCapacity": "Storage Capacity", "electricityConsumption": "Electricity Consumption", "gridExport": "Grid Export", "storageCharge": "Storage Charge", "storageDischarge": "Storage Discharge", "storageBenefit": "Storage Benefit", "pvBenefit": "PV Benefit", "gridExportIncome": "Grid Export Income", "totalBenefit": "Total Benefit", "roi": "ROI", "roiGood": "ROI is good", "roiBad": "ROI is bad", "paybackPeriod": "Payback Period", "notCompleted": "Project analysis not completed", "noData": "No analysis data", "year": "Year", "investment": "Investment Amount", "annualBenefit": "Annual Benefit", "cumulativeCashFlow": "Cumulative Cash Flow", "years": "Years", "perYear": "Per Year", "selfConsumptionRate": "Self-consumption Rate", "financialSummary": "Financial Summary", "annualGeneration": "Annual Generation", "annualPVGeneration": "Annual PV Generation", "annualPVBenefit": "Annual PV Benefit", "specificYield": "Specific Yield", "roiNegative": "ROI is negative, this project is not economically feasible", "paybackShort": "Short payback period, quick return on investment", "overallAdvice": "Overall advice: Please re-evaluate project parameters", "annualConsumption": "Annual Consumption", "annualGridExport": "Annual Grid Export", "annualGridImport": "Annual Grid Import", "annualStorageCharge": "Annual Storage Charge", "annualStorageDischarge": "Annual Storage Discharge", "storageEfficiency": "Storage Efficiency", "totalInvestment": "Total Investment", "gridExportIncomeTitle": "Grid Export Income", "investmentAdvice": "Investment Advice", "annualCashFlow": "Annual Cash Flow", "projectParameters": "Project Parameters", "generationPerKW": "Generation per kW", "benefitPerKW": "Benefit per kW", "pvReturnRate": "PV Annual Return Rate", "storageReturnRate": "Storage Annual Return Rate", "capacityUtilizationRate": "Capacity Utilization Rate", "pvBenefitPerKW": "PV Benefit per kW", "storageBenefitPerKWh": "Storage Benefit per kWh", "reanalyze": "Reanalyze", "analyzing": "Analyzing", "analyzingDescription": "Recalculating project analysis results, please wait...", "reanalyzeSuccess": "Reanalysis successful", "reanalyzeFailed": "Reanalysis failed", "reanalyzeCancelled": "Reanalysis cancelled", "configChangedWarning": "The new configuration has not been reanalyzed, currently showing results before the change", "generateReport": "Generate Analysis Report", "tooltips": {"totalBenefit": "Total annual project benefit, including PV generation benefit and storage arbitrage benefit. Calculation: PV Benefit + Storage Benefit", "roi": "Return on Investment, representing the percentage of annual benefit to total investment. Calculation: Annual Benefit ÷ Total Investment × 100%", "paybackPeriod": "The time required to recover the total investment through project benefits. Calculation: Total Investment ÷ Annual Benefit", "selfConsumptionRate": "The proportion of PV generation used for self-consumption. Calculation: (Total Generation - Grid Export) ÷ Total Generation × 100%", "annualPVGeneration": "Total annual PV system generation in kilowatt-hours (kWh). Calculated based on irradiance data and PV module parameters", "annualConsumption": "Total annual electricity consumption in kilowatt-hours (kWh). Based on electricity usage input data", "annualGridExport": "Total annual electricity exported to the grid in kilowatt-hours (kWh). Occurs when PV generation exceeds consumption and storage is full", "annualGridImport": "Total annual electricity purchased from the grid in kilowatt-hours (kWh). Occurs when consumption exceeds PV generation and storage is depleted", "pvBenefit": "Economic benefit from PV generation, including savings from self-consumption and income from grid export", "storageBenefit": "Economic benefit from the storage system, achieved through price arbitrage by charging during off-peak and discharging during peak hours", "gridExportIncome": "Income from electricity exported to the grid. Calculation: Sum of (Hourly Grid Export × Hourly Feed-in Tariff)", "totalInvestment": "Total project investment, including PV equipment, storage equipment, inverters, and other investment items", "annualPVBenefit": "Annual benefit from the PV system, including savings from self-consumption and income from grid export", "generationPerKW": "Annual generation per kilowatt of PV installed capacity in kilowatt-hours/kilowatt (kWh/kW). Calculation: Annual Generation ÷ Total Installed Capacity", "benefitPerKW": "Annual benefit per kilowatt of PV installed capacity in JPY/kilowatt (JPY/kW). Calculation: Annual PV Benefit ÷ Total Installed Capacity", "annualStorageCharge": "Total annual storage system charging in kilowatt-hours (kWh)", "annualStorageDischarge": "Total annual storage system discharging in kilowatt-hours (kWh)", "storageEfficiency": "Charge/discharge efficiency of the storage system, representing the ratio of discharge to charge. Calculation: Annual Discharge ÷ Annual Charge × 100%", "pvReturnRate": "Annual return rate of the PV system, representing the percentage of PV benefit to PV investment. Calculation: Selected PV Group Annual Benefit ÷ (Selected PV Group Module Cost + Selected PV Group Power/Total PV Power × (Inverter Investment + Other Investment)) × 100%", "storageReturnRate": "Annual return rate of the storage system, representing the percentage of storage benefit to storage investment. Calculation: Annual Storage Benefit ÷ Storage Investment × 100%", "capacityUtilizationRate": "Storage capacity utilization rate, representing the average daily charging amount as a percentage of total capacity. Calculation: Annual Charge ÷ Storage Capacity ÷ 365 days × 100%"}, "advice": {"excellent": "This project has excellent investment returns", "highROI": "ROI is as high as {roi}%", "quickPayback": "Payback period is only {years} years", "recommendProceed": "Strongly recommend proceeding with this project", "good": "This project has good investment returns", "decentROI": "ROI is {roi}%", "reasonablePayback": "Payback period is about {years} years", "considerProceed": "Consider proceeding with this project", "challenging": "This project has challenging investment returns", "lowROI": "ROI is only {roi}%", "longPayback": "Payback period is as long as {years} years", "reconsiderOptions": "Re-evaluate project configuration or look for ways to reduce costs"}}, "databases": {"irradiance": {"title": "Irradiance Database", "new": "New Irradiance Data", "edit": "Edit Irradiance Data", "delete": "Delete Irradiance Data", "name": "Data Name", "location": "Location", "data": "Irradiance Data", "import": "Import Data", "export": "Export Data", "datasetSelection": "Irradiance Dataset Selection"}, "electricityPrice": {"title": "Electricity Price Management", "policyList": "Electricity Price Policy List", "priceVisualization": "Electricity Price Visualization", "noPolicySelected": "No electricity price policy selected", "selectPolicyPrompt": "Please select an electricity price policy from the list above to view", "createPolicy": "Create Electricity Price Policy", "editPolicy": "Edit Electricity Price Policy", "name": "Policy Name", "region": "Applicable Region", "rules": "Electricity Price Rules", "rulesCount": "Number of Rules", "startTime": "Start Time", "endTime": "End Time", "price": "Electricity Price", "gridFeedInPrice": "Feed-in Tariff", "type": "Type", "peak": "Peak", "normal": "Normal", "valley": "Valley", "superPeak": "Super Peak", "hour": "Hour", "daily": "Daily", "weekly": "Weekly", "priceChart": "Electricity Price Chart", "noRules": "No data", "noData": "No data", "noSeasons": "No seasonal policy", "create": "Add New Electricity Price Policy", "dataStatistics": "Data Statistics", "nameRequired": "Please enter policy name", "regionRequired": "Please enter applicable region", "rulesRequired": "Please add at least one electricity price rule", "startTimeRequired": "Please select start time", "endTimeRequired": "Please select end time", "priceRequired": "Please enter electricity price", "gridFeedInPriceRequired": "Please enter feed-in tariff", "typeRequired": "Please select type", "confirmDelete": "Confirm delete", "deleteWarning": "Data cannot be recovered after deletion. Confirm delete?", "deleteSuccess": "Delete successful", "deleteError": "Delete failed", "createSuccess": "Create successful", "createError": "Create failed", "updateSuccess": "Update successful", "updateError": "Update failed", "loadError": "Failed to load data", "namePlaceholder": "Please enter policy name", "regionPlaceholder": "Please enter applicable region", "selectTime": "Please select time", "selectType": "Please select type", "operation": "Operation", "dragToSelect": "Drag to select time range", "hourUnit": "Unit: Hour", "priceUnit": "Unit: Yen/kWh", "timeRangeChart": "24-hour Electricity Price Chart", "policyType": "Policy Type", "policyTypeRequired": "Please select policy type", "fixed": "Fixed Policy", "seasonal": "Seasonal Policy", "seasonalPolicies": "Seasonal Policies", "seasonalPoliciesRequired": "Please add at least one seasonal policy", "addSeason": "Add Season", "newSeason": "New Season", "months": "Months", "seasonsCount": "Number of Seasons", "seasonName": "Season Name", "seasonNameRequired": "Please enter season name", "seasonNamePlaceholder": "Please enter season name", "applicableMonths": "Applicable Months", "monthsRequired": "Please select applicable months", "selectMonths": "Please select months", "noMonthsSelected": "No months selected", "saveChangesPrompt": "Save current changes?", "createdAt": "Created At", "updatedAt": "Updated At"}, "suppliers": {"title": "Suppliers Database", "new": "New Supplier", "edit": "Edit Supplier", "delete": "Delete Supplier", "name": "Supplier Name", "contact": "Contact", "phone": "Phone", "email": "Email", "address": "Address", "products": "Products", "list": "Supplier List", "statistics": "Supplier Statistics", "typeDistribution": "Supplier Type Distribution", "regionDistribution": "Supplier Region Distribution", "typeDistributionChart": "Supplier Type Distribution Chart", "regionDistributionChart": "Supplier Region Distribution Chart", "noData": "No Supplier Data", "createPrompt": "Please click \"New Supplier\" button to create", "nameRequired": "Please enter supplier name", "contactRequired": "Please enter contact", "phoneRequired": "Please enter phone", "emailRequired": "Please enter email", "emailInvalid": "Email format is invalid", "productsHelp": "Separate multiple products with commas", "createSuccess": "Create supplier successfully", "createError": "Failed to create supplier", "updateSuccess": "Update supplier successfully", "updateError": "Failed to update supplier", "deleteSuccess": "Delete supplier successfully", "deleteError": "Failed to delete supplier", "deleteConfirm": "Are you sure you want to delete this supplier? This operation cannot be undone.", "loadError": "Failed to load supplier data", "noSupplierSelected": "No supplier selected", "supplierType": "Supplier Type", "region": "Region", "cooperationStatus": "Cooperation Status", "rating": "Rating", "pvSupplier": "PV Supplier", "storageSupplier": "Storage Supplier", "inverterSupplier": "Inverter Supplier", "otherSupplier": "Other Supplier", "japan": "Japan", "china": "China", "other": "Others", "cooperating": "Cooperating", "negotiating": "Negotiating", "rating5": "5 Stars", "rating4": "4 Stars", "rating3": "3 Stars"}, "equipment": {"title": "Equipment Database", "new": "New Equipment", "edit": "Edit Equipment", "delete": "Delete Equipment", "name": "Equipment Name", "type": "Type", "manufacturer": "Manufacturer", "model": "Model", "specs": "Specifications", "price": "Price", "pv": "PV Module", "storage": "Storage", "inverter": "Inverter", "other": "Other", "supplier": "Supplier", "noSupplier": "No supplier specified", "nameRequired": "Please enter equipment name", "typeRequired": "Please select equipment type", "manufacturerRequired": "Please enter manufacturer", "modelRequired": "Please enter model", "priceRequired": "Please enter price", "createSuccess": "Create equipment successfully", "createError": "Failed to create equipment", "updateSuccess": "Update equipment successfully", "updateError": "Failed to update equipment", "deleteSuccess": "Delete equipment successfully", "deleteError": "Failed to delete equipment", "deleteConfirm": "Are you sure you want to delete this equipment? This operation cannot be undone.", "loadError": "Failed to load equipment data", "noData": "No equipment data", "createPrompt": "Please click \"New Equipment\" button to create", "noEquipmentSelected": "No equipment selected", "selectEquipmentPrompt": "Please select an equipment from the list above to view", "attachments": {"title": "Equipment Attachments", "label": "Attachments", "tooltip": "Upload equipment related files, such as technical manuals, images, etc.", "upload": "Upload Attachment", "manual": "Technical Manual", "image": "Image", "datasheet": "Datasheet", "other": "Other", "noAttachments": "No attachments", "noEquipmentId": "No equipment ID specified, cannot upload attachments", "uploading": "Uploading attachment", "uploadSuccess": "Upload attachment successfully", "uploadError": "Failed to upload attachment", "deleteSuccess": "Delete attachment successfully", "deleteError": "Failed to delete attachment", "deleteConfirm": "Are you sure you want to delete this attachment? This operation cannot be undone.", "moreAttachments": "{count} more attachments", "noAttachmentsText": "No attachments", "cannotPreview": "This file type cannot be previewed", "downloadConfirmTitle": "Download Confirmation", "downloadConfirm": "Do you want to download file {name}?", "downloadSuccess": "Download attachment successfully", "downloadError": "Failed to download attachment", "previewError": "Failed to preview image", "invalidFileType": "Unsupported file type", "fileTooLarge": "File size exceeds limit (max 50MB)", "preview": "Preview Attachment"}, "priceRange": "Price Range", "priceLow": "Low Price (<100,000)", "priceMedium": "Medium Price (100,000-500,000)", "priceHigh": "High Price (>500,000)", "all": "All Equipment", "detail": "Equipment Detail", "basicInfo": "Basic Info", "description": "Description", "noDescription": "No description", "pvSpecs": {"power": "Power", "powerTooltip": "Rated power of the PV module, in watts (W)", "powerRequired": "Please enter power", "efficiency": "Conversion Efficiency", "efficiencyTooltip": "Photoelectric conversion efficiency of the PV module, in percent (%)", "efficiencyRequired": "Please enter efficiency", "area": "Area", "areaTooltip": "Physical area of the PV module, in square meters (m²)", "areaRequired": "Please enter area", "warranty": "Warranty Period", "warrantyRequired": "Please enter warranty period", "years": "Years", "degradation": "Annual Degradation Rate", "degradationTooltip": "Annual performance degradation rate of the PV module, in percent (%)", "degradationRequired": "Please enter degradation rate", "length": "Length", "lengthTooltip": "Length of the PV module, in millimeters (mm)", "lengthRequired": "Please enter length", "width": "<PERSON><PERSON><PERSON>", "widthTooltip": "Width of the PV module, in millimeters (mm)", "widthRequired": "Please enter width", "height": "Height", "heightTooltip": "Height of the PV module, in millimeters (mm)", "heightRequired": "Please enter height", "dimensions": "Dimensions (L×W×H)"}, "storageSpecs": {"capacity": "Capacity", "capacityTooltip": "Rated capacity of the storage device, in kilowatt-hours (kWh)", "capacityRequired": "Please enter capacity", "power": "Power", "powerTooltip": "Rated power of the storage device, in kilowatts (kW)", "powerRequired": "Please enter power", "efficiency": "Charge/Discharge Efficiency", "efficiencyTooltip": "Charge/discharge efficiency of the storage device, in percent (%)", "efficiencyRequired": "Please enter efficiency", "cycles": "Cycles", "cyclesTooltip": "Rated cycle count of the storage device", "cyclesRequired": "Please enter cycles", "warranty": "Warranty Period", "warrantyRequired": "Please enter warranty period", "years": "Years", "depthOfDischarge": "Depth of Discharge", "depthOfDischargeTooltip": "Maximum depth of discharge, in percent (%)", "depthOfDischargeRequired": "Please enter depth of discharge", "length": "Length", "lengthTooltip": "Length of the storage device, in millimeters (mm)", "lengthRequired": "Please enter length", "width": "<PERSON><PERSON><PERSON>", "widthTooltip": "Width of the storage device, in millimeters (mm)", "widthRequired": "Please enter width", "height": "Height", "heightTooltip": "Height of the storage device, in millimeters (mm)", "heightRequired": "Please enter height", "dimensions": "Dimensions (L×W×H)"}, "inverterSpecs": {"power": "Power", "powerTooltip": "Rated power of the inverter, in kilowatts (kW)", "powerRequired": "Please enter power", "efficiency": "Conversion Efficiency", "efficiencyTooltip": "Conversion efficiency of the inverter, in percent (%)", "efficiencyRequired": "Please enter efficiency", "mpptRange": "MPPT Voltage Range", "minVoltage": "Min Voltage", "maxVoltage": "Max Voltage", "minVoltageRequired": "Please enter min voltage", "maxVoltageRequired": "Please enter max voltage", "warranty": "Warranty Period", "warrantyRequired": "Please enter warranty period", "years": "Years", "length": "Length", "lengthTooltip": "Length of the inverter, in millimeters (mm)", "lengthRequired": "Please enter length", "width": "<PERSON><PERSON><PERSON>", "widthTooltip": "Width of the inverter, in millimeters (mm)", "widthRequired": "Please enter width", "height": "Height", "heightTooltip": "Height of the inverter, in millimeters (mm)", "heightRequired": "Please enter height", "dimensions": "Dimensions (L×W×H)"}}}, "settings": {"general": {"title": "General Settings", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "timezone": "Timezone", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System", "displaySettings": "Display Settings", "regionSettings": "Region Settings", "siteSettings": "Site Settings", "siteName": "Site Name", "siteLogo": "Site Logo", "siteNameRequired": "Please enter site name", "siteLogoRequired": "Please upload site logo", "uploadLogo": "Upload Logo", "saveSuccess": "Setting<PERSON> saved successfully", "saveError": "Failed to save settings", "resetSuccess": "Settings reset successfully", "languageChanged": "Language changed", "themeChanged": "Theme changed"}, "account": {"title": "Account", "profile": "Profile", "security": "Security", "changePassword": "Change Password", "twoFactor": "Two-Factor Authentication", "username": "Username", "email": "Email", "password": "Password", "role": "Role", "status": "Status", "lastLogin": "Last Login", "admin": "Admin", "user": "User", "active": "Active", "inactive": "Inactive", "addUser": "Add User", "editUser": "Edit User", "deleteUserTitle": "Delete User", "deleteUserConfirm": "Are you sure you want to delete this user? This operation cannot be undone.", "userDeleted": "User deleted", "userAdded": "User added", "userUpdated": "User updated", "invalidEmail": "Invalid email format"}, "notifications": {"title": "Notifications", "email": "Email Notifications", "system": "System Notifications", "projectComplete": "Project Analysis Complete", "updates": "System Updates", "emailTooltip": "Receive notifications via email", "systemTooltip": "Receive notifications within the system", "channelSettings": "Notification Channel Settings", "eventSettings": "Notification Event Settings", "frequencySettings": "Notification Frequency Settings", "frequency": "Notification Frequency", "immediate": "Immediate Notification", "daily": "Daily Summary", "weekly": "Weekly Summary", "saveSuccess": "Notification settings saved successfully", "resetSuccess": "Notification settings reset"}, "backup": {"title": "Backup", "autoBackup": "Auto Backup", "backupNow": "Backup Now", "restore": "Rest<PERSON>", "backupHistory": "Backup History", "lastBackup": "Last Backup", "autoSettings": "Auto Backup Settings", "backupInterval": "Backup Interval", "backupTime": "Backup Time", "maxBackups": "<PERSON> Backup Count", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "name": "Backup Name", "date": "Backup Date", "size": "Backup Size", "type": "Backup Type", "auto": "Auto Backup", "manual": "Manual Backup", "deleteBackupTitle": "Delete Backup", "deleteBackupConfirm": "Are you sure you want to delete this backup? This operation cannot be undone.", "deleteSuccess": "Backup deleted", "backupSuccess": "Backup successful", "restoreConfirm": "Are you sure you want to restore this backup? Current data will be overwritten.", "saveSuccess": "Backup settings saved successfully"}, "about": {"title": "About", "version": "Version", "developer": "Developer", "license": "License", "contact": "Contact Us", "website": "Website", "appInfo": "App Information", "techStack": "Tech Stack", "description": "Description", "descriptionContent": "PV+Storage Project Economic Analysis System is a professional analysis tool for evaluating the economic feasibility of photovoltaic and energy storage projects. The system provides comprehensive data analysis and visualization functions to help users make informed investment decisions.", "acknowledgements": "Acknowledgements", "acknowledgementsContent": "Thanks to all developers and users who have contributed to this project.", "copyright": "Copyright", "subtitle": "PV+Storage Project Economic Analysis System", "repository": "Code Repository"}, "clearData": "Clear Data", "clearDataDescription": "Clear virtual data in the system to upload real data.", "clearDataHelp": "Clear Data Help", "clearDataHelpContent": "Clearing data will delete virtual data in the system, including project library and irradiance database data. This operation cannot be undone.", "clearProjectsData": "Clear Project Library Data", "clearIrradianceData": "Clear Irradiance Database Data", "clearAllData": "Clear All Virtual Data", "clearProjectsDataTitle": "Clear Project Library Data", "clearProjectsDataConfirm": "Are you sure you want to clear all project library data? This operation cannot be undone.", "clearIrradianceDataTitle": "Clear Irradiance Database Data", "clearIrradianceDataConfirm": "Are you sure you want to clear all irradiance database data? This operation cannot be undone.", "clearAllDataTitle": "Clear All Virtual Data", "clearAllDataConfirm": "Are you sure you want to clear all virtual data in the system? Including project library and irradiance database data. This operation cannot be undone.", "dataCleared": "Data cleared", "projectsDataCleared": "Project library data cleared", "irradianceDataCleared": "Irradiance database data cleared", "cache": {"title": "Cache Management", "usageStatistics": "<PERSON><PERSON>", "usedSpace": "Used Space", "totalSpace": "Total Space", "itemCount": "<PERSON><PERSON>", "cachedItems": "<PERSON><PERSON> List", "dataName": "Data Name", "dataSize": "Data Size", "lastAccessed": "Last Accessed", "clearAllCache": "Clear All Cache", "clearAllCacheTitle": "Clear All Cache", "clearAllCacheConfirm": "Are you sure you want to clear all cache? This will delete all locally stored data and will be re-downloaded from the server if needed.", "allCacheCleared": "All cache cleared", "deleteCacheItemTitle": "Delete Cache Item", "deleteCacheItemConfirm": "Are you sure you want to delete this cache item?", "cacheItemDeleted": "Cache item deleted", "refresh": "Refresh", "statsRefreshed": "Cache statistics refreshed", "fetchStatsFailed": "Failed to fetch cache statistics", "helpTitle": "Cache Management Help", "helpContent": "Cache management is used to view and manage data stored locally in the browser. When cache space is insufficient, you can manually delete unused items or clear all cache. After clearing, data will be re-downloaded from the server if needed.", "almostFull": "Almost Full", "moderate": "Moderate", "good": "Good"}}, "common": {"save": "Save", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "import": "Import", "export": "Export", "upload": "Upload", "download": "Download", "refresh": "Refresh", "select": "Select", "yes": "Yes", "no": "No", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "loading": "Loading", "calculating": "Calculating", "noData": "No Data", "required": "Required", "optional": "Optional", "finish": "Finish", "backToList": "Back to List", "details": "Details", "pleaseCompleteRequired": "Please complete required fields", "saveFailed": "Save failed", "pleaseSelect": "Please select", "pleaseInput": "Please input", "all": "All", "operation": "Operation", "syncStatus": "Sync Status", "synced": "Synced", "syncedTooltip": "Data is available both locally and on the server", "localOnly": "Local Only", "localOnlyTooltip": "Data is only available locally and not yet synced to the server", "serverOnly": "Server Only", "fullYear": "Full Year", "dayUnit": "Day", "monthUnit": "Month", "serverOnlyTooltip": "Data is only available on the server and will be downloaded when selected", "syncing": "Syncing", "fileTypeError": "File type error, please upload {type} file", "fileSizeError": "File size exceeds limit, maximum {size}MB", "actions": "Actions", "navigationConfirmTitle": "Confirm Navigation", "navigationConfirmContent": "You are creating a new project. Leaving this page will discard unsaved changes. What would you like to do?", "stayOnPage": "Stay on this page", "discardChanges": "Discard changes", "add": "Add", "update": "Update", "preview": "Preview", "close": "Close", "replace": "Replace", "fileSize": "File Size", "currency": "Yen/kWh", "saveAsImage": "Save as Image", "pleaseSelectOption": "Please select", "pleaseInputText": "Please input", "reset": "Reset", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "month1": "January", "month2": "February", "month3": "March", "month4": "April", "month5": "May", "month6": "June", "month7": "July", "month8": "August", "month9": "September", "month10": "October", "month11": "November", "month12": "December"}, "irradiance": {"title": "Irradiance Data Management", "storageMode": "Storage Mode", "localAndServer": "Local Storage", "datasetList": "Irradiance Dataset List", "dataUpload": "Data Upload", "uploadDescription": "Upload CSV format irradiance data file, supporting viewing different metrics by month, day, hour", "selectCSV": "Select CSV File", "dataVisualization": "Irradiance Data Visualization", "noDataSelected": "No Dataset Selected", "selectDataPrompt": "Please select a dataset from the list above to view", "metric": "Metric", "viewMode": "View Mode", "month": "Month", "day": "Day", "monthUnit": "Month", "dayUnit": "Day", "hourly": "Hourly", "daily": "Daily", "monthly": "Monthly", "fullYear": "Full Year", "allYear": "All Year", "globalHorizontalIrradiance": "Global Horizontal Irradiance", "temperature": "Temperature", "directTime": "Direct Time", "dataStatistics": "Data Statistics", "name": "Dataset Name", "location": "Location", "coordinates": "Coordinates", "createdAt": "Created At", "year": "Data Year", "dataSize": "Data Size", "upload": "Add Irradiance Data", "uploadTitle": "Upload Irradiance Data", "dataInfo": "Irradiance Data Info", "file": "File", "fileRequired": "Please select a file", "nameRequired": "Please enter dataset name", "locationRequired": "Please enter location", "latitude": "Latitude", "longitude": "Longitude", "latitudeRequired": "Please enter latitude", "longitudeRequired": "Please enter longitude", "yearRequired": "Please enter data year", "fetchError": "Failed to fetch irradiance data list", "fetchDetailError": "Failed to fetch irradiance data details", "confirmDelete": "Confirm Delete", "deleteWarning": "Data cannot be recovered after deletion, confirm delete?", "deleteSuccess": "Delete successful", "deleteError": "Delete failed", "parseError": "Failed to parse CSV file", "uploadSuccess": "Upload successful", "uploadError": "Upload failed", "downloadingData": "Downloading data from server...", "downloadSuccess": "Data download successful", "downloadError": "Data download failed", "loadError": "Failed to load data", "dataNotFound": "Data not found", "dataEmpty": "Data is empty or format error", "replaceSuccess": "Replace successful", "updateSuccess": "Update successful", "updateError": "Update failed"}, "electricityPrice": {"title": "Electricity Price Management", "policyList": "Electricity Price Policy List", "priceVisualization": "Electricity Price Visualization", "noPolicySelected": "No electricity price policy selected", "selectPolicyPrompt": "Please select an electricity price policy from the list above to view", "createPolicy": "Create Electricity Price Policy", "editPolicy": "Edit Electricity Price Policy", "name": "Policy Name", "region": "Applicable Region", "rules": "Electricity Price Rules", "rulesCount": "Number of Rules", "startTime": "Start Time", "endTime": "End Time", "price": "Electricity Price", "gridFeedInPrice": "Feed-in Tariff", "type": "Type", "peak": "Peak", "normal": "Normal", "valley": "Valley", "superPeak": "Super Peak", "hour": "Hour", "daily": "Daily", "weekly": "Weekly", "priceChart": "Electricity Price Chart", "noRules": "No data", "noData": "No data", "noSeasons": "No seasonal policy", "create": "Add New Electricity Price Policy", "dataStatistics": "Data Statistics", "nameRequired": "Please enter policy name", "regionRequired": "Please enter applicable region", "rulesRequired": "Please add at least one electricity price rule", "startTimeRequired": "Please select start time", "endTimeRequired": "Please select end time", "priceRequired": "Please enter electricity price", "gridFeedInPriceRequired": "Please enter feed-in tariff", "typeRequired": "Please select type", "confirmDelete": "Confirm delete", "deleteWarning": "Data cannot be recovered after deletion. Confirm delete?", "deleteSuccess": "Delete successful", "deleteError": "Delete failed", "createSuccess": "Create successful", "createError": "Create failed", "updateSuccess": "Update successful", "updateError": "Update failed", "loadError": "Failed to load data", "namePlaceholder": "Please enter policy name", "regionPlaceholder": "Please enter applicable region", "selectTime": "Please select time", "selectType": "Please select type", "operation": "Operation", "dragToSelect": "Drag to select time range", "hourUnit": "Unit: Hour", "priceUnit": "Unit: Yen/kWh", "timeRangeChart": "24-hour Electricity Price Chart", "policyType": "Policy Type", "policyTypeRequired": "Please select policy type", "fixed": "Fixed Policy", "seasonal": "Seasonal Policy", "seasonalPolicies": "Seasonal Policies", "seasonalPoliciesRequired": "Please add at least one seasonal policy", "addSeason": "Add Season", "newSeason": "New Season", "months": "Months", "seasonsCount": "Number of Seasons", "seasonName": "Season Name", "seasonNameRequired": "Please enter season name", "seasonNamePlaceholder": "Please enter season name", "applicableMonths": "Applicable Months", "monthsRequired": "Please select applicable months", "selectMonths": "Please select months", "noMonthsSelected": "No months selected", "saveChangesPrompt": "Save current changes?", "createdAt": "Created At", "updatedAt": "Updated At"}, "chart": {"hour": "Hour", "day": "Day", "month": "Month", "irradiance": "Irradiance", "temperature": "Temperature", "directTime": "Direct Time", "avgIrradiance": "Average Irradiance", "totalIrradiance": "Total Irradiance", "minIrradiance": "Minimum Irradiance", "hourlyTemperature": "Hourly Temperature", "avgTemperature": "Average Temperature", "maxTemperature": "Maximum Temperature", "minTemperature": "Minimum Temperature", "monthlyDirectTime": "Monthly Direct Time", "cumulativeDirectTime": "Cumulative Direct Time", "noData": "No Data", "metric": "Metric", "viewMode": "View Mode", "hourly": "Hourly", "daily": "Daily", "monthly": "Monthly", "generation": "Generation", "income": "Income", "cumulativeGeneration": "Cumulative Generation", "cumulativeIncome": "Cumulative Income", "monthlyPVGeneration": "Monthly PV Generation", "monthlyPVIncome": "Monthly PV Income", "dailyPVGeneration": "Daily PV Generation", "dailyPVIncome": "Daily PV Income", "hourlyPVGeneration": "Hourly PV Generation", "hourlyPVIncome": "Hourly PV Income", "date": "Date", "error": "Chart Error", "allYear": "All Year", "dayUnit": "Day", "monthUnit": "Month", "visualizationOptions": "Visualization Options", "metrics": "Metrics Selection", "storageCharge": "Storage Charge", "storageDischarge": "Storage Discharge", "storageCapacity": "Storage Capacity", "pvGeneration": "PV Generation Total", "electricityConsumption": "User Consumption", "gridExport": "Grid Export", "gridImport": "Grid Import", "storageBenefit": "Storage Benefit", "cumulativeStorageBenefit": "Cumulative Storage Benefit", "hourlyStorageAnalysis": "Hourly Storage Analysis", "dailyStorageAnalysis": "Daily Storage Analysis", "monthlyStorageAnalysis": "Monthly Storage Analysis", "energy": "Energy", "benefit": "Benefit"}, "pvModules": {"title": "PV Modules", "addModule": "<PERSON><PERSON>", "editModule": "<PERSON>", "deleteModule": "Delete Module", "confirmDelete": "Confirm Delete Module", "deleteConfirmMessage": "Are you sure you want to delete this module? This operation cannot be undone.", "deleteSuccess": "Delete module successful", "deleteFailed": "Delete module failed", "editSuccess": "Edit module successful", "addSuccess": "Add module successful", "updateFailed": "Update module failed", "name": "Module Name", "manufacturer": "Manufacturer", "model": "Model", "power": "Power", "efficiency": "Efficiency", "area": "Area", "price": "Price", "quantity": "Quantity", "angle": "Installation Angle", "angleTooltip": "Angle between the PV panel and the horizontal plane", "orientation": "Orientation", "orientationAngle": "Orientation Angle", "orientationAngleHelp": "Angle from due south, negative for east, positive for west", "totalPower": "Total Power", "totalArea": "Total Area", "totalCost": "Total Cost", "summary": "Summary", "totalModules": "Total Module Types", "totalQuantity": "Total Quantity", "noModules": "No Modules", "moduleList": "Module List", "equipmentList": "Equipment List", "selectedModules": "Selected Modules", "noEquipment": "No Equipment", "selectEquipment": "Select Equipment", "selectEquipmentPlaceholder": "Please select equipment", "selectOrientation": "Please select orientation", "orientationRequired": "Please select orientation", "angleRequired": "Please enter installation angle", "basicInfo": "Basic Information", "specifications": "Specifications", "south": "South", "north": "North", "east": "East", "west": "West", "northeast": "Northeast", "northwest": "Northwest", "southeast": "Southeast", "southwest": "Southwest", "custom": "Custom", "nameRequired": "Please enter module name", "manufacturerRequired": "Please enter manufacturer", "modelRequired": "Please enter model", "powerRequired": "Please enter power", "efficiencyRequired": "Please enter efficiency", "areaRequired": "Please enter area", "priceRequired": "Please enter price", "quantityRequired": "Please enter quantity"}, "energyStorage": {"title": "Energy Storage", "addDevice": "Add <PERSON>", "editDevice": "<PERSON>", "deleteDevice": "Delete Device", "name": "Device Name", "manufacturer": "Manufacturer", "model": "Model", "capacity": "Capacity", "power": "Power", "efficiency": "Efficiency", "cycles": "Cycles", "price": "Price", "quantity": "Quantity", "totalCapacity": "Total Capacity", "totalPower": "Total Power", "totalCost": "Total Cost", "summary": "Summary", "totalDevices": "Total Device Types", "totalQuantity": "Total Quantity", "noDevices": "No Devices", "noStorage": "No storage devices", "storageList": "Storage Device List", "equipmentList": "Equipment List", "selectedDevices": "Selected Devices", "noEquipment": "No Equipment", "selectEquipment": "Select Equipment", "selectEquipmentPlaceholder": "Please select equipment", "basicInfo": "Basic Info", "specifications": "Specifications", "nameRequired": "Please enter device name", "manufacturerRequired": "Please enter manufacturer", "modelRequired": "Please enter model", "capacityRequired": "Please enter capacity", "powerRequired": "Please enter power", "efficiencyRequired": "Please enter efficiency", "cyclesRequired": "Please enter cycles", "priceRequired": "Please enter price", "quantityRequired": "Please enter quantity", "addSuccess": "Add device successful", "editSuccess": "Edit device successful", "deleteSuccess": "Delete device successful", "updateFailed": "Update device failed", "deleteFailed": "Delete device failed", "confirmDelete": "Confirm Delete Device", "deleteConfirmMessage": "Are you sure you want to delete this device? This operation cannot be undone."}, "inverters": {"title": "Inverters", "addInverter": "Add Inverter", "editInverter": "Edit Inverter", "deleteInverter": "Delete Inverter", "name": "Inverter Name", "manufacturer": "Manufacturer", "model": "Model", "power": "Power", "efficiency": "Efficiency", "price": "Price", "quantity": "Quantity", "totalPower": "Total Power", "totalCost": "Total Cost", "summary": "Summary", "totalInverters": "Total Inverter Types", "totalQuantity": "Total Quantity", "noInverters": "No Inverters", "equipmentList": "Equipment List", "selectedInverters": "Selected Inverters", "noEquipment": "No Equipment", "selectEquipment": "Select Equipment", "selectEquipmentPlaceholder": "Please select equipment", "basicInfo": "Basic Info", "specifications": "Specifications"}, "electricityUsage": {"title": "Electricity Usage Data", "dataType": "Data Type", "hourly": "Hourly", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "value": "Usage Value", "hour": "Hour", "day": "Day", "week": "Week", "month": "Month", "usageData": "Usage Data", "addData": "Add Data", "editDataAction": "Edit Data", "deleteData": "Delete Data", "noData": "No Data", "hourlyData": "Hourly Data", "dailyData": "Daily Data", "weeklyData": "Weekly Data", "monthlyData": "Monthly Data", "manualInput": "Manual Input", "fileUpload": "File Upload", "clickOrDrag": "Click or drag file to this area to upload", "supportedFormats": "Supported formats: CSV", "uploadSuccess": "Upload successful", "uploadError": "Upload failed", "parseError": "Failed to parse file", "templateDownload": "Download Template", "sameEveryday": "Same Every Day", "sameEveryWeek": "Same Every Week", "monthlyDifferent": "Different by Month", "dailyDifferent": "Different Every Day", "patternByDay": "Pattern by Day of Week", "patternByMonth": "Pattern by Month", "patternByDayDescription": "Create multiple 24-hour usage patterns, each applicable to different days of the week", "patternByMonthDescription": "Create multiple 24-hour usage patterns, each applicable to different months of the year", "addDayPattern": "Add Day Pattern", "addMonthPattern": "Add Month Pattern", "newDayPattern": "New Day Pattern", "newMonthPattern": "New Month Pattern", "patternName": "Pattern Name", "patternNamePlaceholder": "Please enter pattern name", "patternNameRequired": "Please enter pattern name", "daysRequired": "Please select applicable days", "monthsRequired": "Please select applicable months", "days": "Days", "months": "Months", "applicableDays": "Applicable Days", "applicableMonths": "Applicable Months", "noDaysSelected": "No days selected", "noMonthsSelected": "No months selected", "saveChangesPrompt": "Save changes?", "selectDayOfWeek": "Select applicable days", "selectMonth": "Select applicable months", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "weekday": "Weekday", "weekend": "Weekend", "spring": "Spring", "summer": "Summer", "autumn": "Autumn", "winter": "Winter", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "hourlyChart": "24-hour Usage Chart", "dailyChart": "Daily Usage Chart", "monthlyChart": "Monthly Usage Chart", "yearlyChart": "Yearly Usage Chart", "kWh": "kWh", "totalConsumption": "Total Consumption", "averageConsumption": "Average Consumption", "peakConsumption": "Peak Consumption", "valleyConsumption": "Valley Consumption", "dataRequired": "Please enter usage data", "uploadTemplate": "Upload Template", "downloadTemplate": "Download Template", "templateDescription": "Please fill in the data according to the template format and upload", "completeInput": "Complete Input", "editDataButton": "Edit Data", "usageChart": "Usage Chart", "viewMode": "View Mode", "inputData": "Input Data", "visualization": "Data Visualization", "noDataToVisualize": "No data to visualize, please input usage data first", "calculating": "Calculating..."}, "otherInvestments": {"title": "Other Investments", "addInvestment": "Add Investment", "editInvestment": "Edit Investment", "deleteInvestment": "Delete Investment", "name": "Investment Name", "category": "Category", "price": "Price", "description": "Description", "totalInvestment": "Total Investment", "totalItems": "Total Items", "summary": "Summary", "noInvestments": "No Investments", "namePlaceholder": "Please enter investment name", "selectCategory": "Please select category", "descriptionPlaceholder": "Please enter description", "nameRequired": "Please enter investment name", "categoryRequired": "Please select category", "priceRequired": "Please enter price", "categories": {"installation": "Installation Cost", "design": "Design Cost", "permit": "Permit Cost", "construction": "Construction Cost", "maintenance": "Maintenance Cost", "other": "Other Cost"}}, "report": {"title": "Project Analysis Report", "generating": "Generating report...", "generateSuccess": "Report generated successfully", "generateFailed": "Failed to generate report", "downloadPDF": "Download PDF Report", "loading": "Loading report content...", "preparingCharts": "Preparing chart data, please wait...", "basicInfo": "Basic Information", "equipmentInfo": "Equipment Information", "economicAnalysis": "Economic Analysis", "generationAnalysis": "Generation Analysis", "storageAnalysis": "Storage Analysis", "investmentDetails": "Investment Details", "generatedDate": "Generated Date", "footer": "© PV+Storage Project Economic Analysis System", "downloadLongImage": "Export Long Image", "generatingLongImage": "Generating long image...", "generateLongImageSuccess": "Long image generated successfully"}}