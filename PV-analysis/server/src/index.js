/**
 * 服务器入口文件
 * 光伏+储能项目经济性分析系统 - 服务器端数据存储服务
 */
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const fs = require('fs');
const path = require('path');

// 导入路由
const irradianceRoutes = require('./routes/irradiance');
const electricityPriceRoutes = require('./routes/electricityPrice');
const supplierRoutes = require('./routes/supplier');
const equipmentRoutes = require('./routes/equipment');
const attachmentRoutes = require('./routes/attachment');
const projectRoutes = require('./routes/project');
const settingsRoutes = require('./routes/settings');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3001;

// 确保数据目录存在
const dataDir = path.join(__dirname, '../data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 确保上传目录存在
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// 中间件
app.use(cors()); // 允许跨域请求
app.use(morgan('dev')); // 日志记录
app.use(express.json({ limit: '50mb' })); // 解析JSON请求体，增加限制以处理大型数据
app.use(express.urlencoded({ extended: true, limit: '50mb' })); // 解析URL编码的请求体

// 路由
app.use('/api/irradiance', irradianceRoutes);
app.use('/api/electricity-prices', electricityPriceRoutes);
app.use('/api/suppliers', supplierRoutes);
app.use('/api/equipment', equipmentRoutes);
app.use('/api/attachments', attachmentRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/settings', settingsRoutes);

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// 根路由
app.get('/', (req, res) => {
  res.json({
    message: '光伏+储能项目经济性分析系统 - 服务器端数据存储服务',
    status: 'running',
    endpoints: [
      '/api/irradiance',
      '/api/electricity-prices',
      '/api/suppliers',
      '/api/equipment',
      '/api/attachments',
      '/api/projects',
      '/api/settings',
      '/api/health'
    ]
  });
});

// 健康检查端点
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    message: '服务器正常运行'
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: err.message || '服务器内部错误'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器已启动，监听端口: ${PORT}`);
  console.log(`访问地址: http://localhost:${PORT}`);
});
