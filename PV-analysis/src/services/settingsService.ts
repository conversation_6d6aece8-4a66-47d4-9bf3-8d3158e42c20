/**
 * 设置服务
 * 用于处理系统设置的保存和获取
 */
import { message } from 'antd';
import { get, post, put } from './api';
import { SettingsState } from '../store/slices/settingsSlice';

// API响应类型
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

/**
 * 获取系统设置
 * @returns Promise
 */
export const getSettings = async (): Promise<SettingsState> => {
  try {
    console.log('开始获取系统设置');

    // 从服务器获取数据
    const response = await get<ApiResponse<SettingsState>>('/settings');
    
    if (response.data.success) {
      const settings = response.data.data;
      
      // 确保logo URL是完整的URL
      if (settings.siteLogo && !settings.siteLogo.startsWith('http')) {
        // 添加服务器基础URL
        const baseUrl = window.location.origin;
        // 确保URL格式正确
        const url = settings.siteLogo.startsWith('/') ? settings.siteLogo : `/${settings.siteLogo}`;
        settings.siteLogo = `${baseUrl}${url}`;
      }
      
      return settings;
    } else {
      throw new Error(response.data.message || '获取系统设置失败');
    }
  } catch (error) {
    console.error('获取系统设置失败:', error);
    throw error;
  }
};

/**
 * 更新系统设置
 * @param settings 设置数据
 * @returns Promise
 */
export const updateSettings = async (settings: Partial<SettingsState>): Promise<SettingsState> => {
  try {
    console.log('开始更新系统设置');

    // 发送到服务器
    const response = await put<ApiResponse<SettingsState>>('/settings', settings);
    
    if (response.data.success) {
      message.success('设置保存成功');
      return response.data.data;
    } else {
      throw new Error(response.data.message || '更新系统设置失败');
    }
  } catch (error) {
    console.error('更新系统设置失败:', error);
    message.error('设置保存失败');
    throw error;
  }
};

/**
 * 上传网站Logo
 * @param file Logo文件
 * @returns Promise
 */
export const uploadLogo = async (file: File): Promise<string> => {
  try {
    console.log('开始上传网站Logo');

    // 创建FormData对象
    const formData = new FormData();
    formData.append('logo', file);

    // 调用服务器API上传文件
    const response = await post<ApiResponse<{ siteLogo: string }>>('/settings/upload-logo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      }
    });
    
    if (response.data.success) {
      // 确保logo URL是完整的URL
      let logoUrl = response.data.data.siteLogo;
      if (logoUrl && !logoUrl.startsWith('http')) {
        // 添加服务器基础URL
        const baseUrl = window.location.origin;
        // 确保URL格式正确
        const url = logoUrl.startsWith('/') ? logoUrl : `/${logoUrl}`;
        logoUrl = `${baseUrl}${url}`;
      }
      
      message.success('Logo上传成功');
      return logoUrl;
    } else {
      throw new Error(response.data.message || '上传Logo失败');
    }
  } catch (error) {
    console.error('上传Logo失败:', error);
    message.error('Logo上传失败');
    throw error;
  }
};
