import React, { useState, useEffect } from 'react';
import { Card, Form, Select, Radio, Button, Space, message, Divider, Typography, Input, Upload } from 'antd';
import { SettingOutlined, GlobalOutlined, DollarOutlined, ClockCircleOutlined, BgColorsOutlined, UploadOutlined, FontSizeOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import { setLanguage, setCurrency, setTimezone, setTheme, setSiteName, setSiteLogo } from '../../store/slices/settingsSlice';
import { LanguageType, CurrencyType, TimezoneType, ThemeType } from '../../types/settings';
import type { UploadChangeParam } from 'antd/es/upload';
import { getSettings, updateSettings, uploadLogo } from '../../services/settingsService';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 常规设置面板组件
 */
const GeneralSettingsPanel: React.FC = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useAppDispatch();
  const { language, currency, timezone, theme, siteName, siteLogo } = useAppSelector((state) => state.settings);
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [logoUrl, setLogoUrl] = useState<string>(siteLogo || '');

  // 语言选项
  const languageOptions = [
    { value: 'zh', label: '中文' },
    { value: 'en', label: 'English' },
    { value: 'ja', label: '日本語' }
  ];

  // 货币选项
  const currencyOptions = [
    { value: 'JPY', label: '日元 (JPY)' },
    { value: 'CNY', label: '人民币 (CNY)' },
    { value: 'USD', label: '美元 (USD)' }
  ];

  // 时区选项
  const timezoneOptions = [
    { value: 'Asia/Tokyo', label: '东京 (UTC+9)' },
    { value: 'Asia/Shanghai', label: '上海 (UTC+8)' },
    { value: 'America/New_York', label: '纽约 (UTC-5)' },
    { value: 'Europe/London', label: '伦敦 (UTC+0)' }
  ];

  // 主题选项
  const themeOptions = [
    { value: 'light', label: t('settings.general.light') },
    { value: 'dark', label: t('settings.general.dark') },
    { value: 'system', label: t('settings.general.system') }
  ];

  // 初始化时从服务器获取设置
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const serverSettings = await getSettings();
        // 更新Redux store
        if (serverSettings.language !== language) {
          dispatch(setLanguage(serverSettings.language));
          i18n.changeLanguage(serverSettings.language);
        }
        if (serverSettings.currency !== currency) {
          dispatch(setCurrency(serverSettings.currency));
        }
        if (serverSettings.timezone !== timezone) {
          dispatch(setTimezone(serverSettings.timezone));
        }
        if (serverSettings.theme !== theme) {
          dispatch(setTheme(serverSettings.theme as 'light' | 'dark' | 'system'));
        }
        if (serverSettings.siteName !== siteName) {
          dispatch(setSiteName(serverSettings.siteName));
        }
        if (serverSettings.siteLogo !== siteLogo) {
          dispatch(setSiteLogo(serverSettings.siteLogo));
          setLogoUrl(serverSettings.siteLogo);
        }

        // 更新表单（不包含language字段）
        form.setFieldsValue({
          currency: serverSettings.currency,
          timezone: serverSettings.timezone,
          theme: serverSettings.theme,
          siteName: serverSettings.siteName,
          siteLogo: serverSettings.siteLogo
        });
      } catch (error) {
        console.error('获取设置失败:', error);
        message.error('获取设置失败');
      }
    };

    fetchSettings();
  }, []);

  // 处理Logo上传
  const handleLogoChange = async (info: UploadChangeParam) => {
    if (info.file.status === 'done') {
      try {
        // 显示上传中状态
        message.loading({
          content: '正在上传Logo...',
          key: 'uploadLogo',
          duration: 0
        });

        // 上传Logo到服务器
        const logoUrl = await uploadLogo(info.file.originFileObj as File);

        // 更新状态和表单
        setLogoUrl(logoUrl);
        form.setFieldsValue({ siteLogo: logoUrl });

        // 更新Redux store
        dispatch(setSiteLogo(logoUrl));

        // 显示成功消息
        message.success({
          content: '上传Logo成功',
          key: 'uploadLogo',
          duration: 2
        });
      } catch (error) {
        console.error('上传Logo失败:', error);
        message.error({
          content: '上传Logo失败',
          key: 'uploadLogo',
          duration: 2
        });
      }
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: {
    currency: CurrencyType;
    timezone: TimezoneType;
    theme: ThemeType;
    siteName: string;
    siteLogo: string;
  }) => {
    setIsLoading(true);

    try {
      // 准备要更新的设置
      const settingsToUpdate = {
        language, // 使用当前语言设置，不从表单获取
        currency: values.currency,
        timezone: values.timezone,
        theme: values.theme,
        siteName: values.siteName,
        // 不包含siteLogo，因为它是通过单独的API上传的
      };

      // 更新服务器设置
      await updateSettings(settingsToUpdate);

      // 更新Redux store
      // 更新货币
      if (values.currency !== currency) {
        dispatch(setCurrency(values.currency));
      }

      // 更新时区
      if (values.timezone !== timezone) {
        dispatch(setTimezone(values.timezone));
      }

      // 更新主题
      if (values.theme !== theme) {
        dispatch(setTheme(values.theme));
      }

      // 更新网站名称
      if (values.siteName !== siteName) {
        dispatch(setSiteName(values.siteName));
      }

      message.success(t('settings.general.saveSuccess'));
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error(t('settings.general.saveError'));
    } finally {
      setIsLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    message.info(t('settings.general.resetSuccess'));
  };

  return (
    <Card
      title={
        <Space>
          <SettingOutlined />
          {t('settings.general.title')}
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ currency, timezone, theme, siteName, siteLogo }}
        onFinish={handleSubmit}
      >
        <Title level={5}>{t('settings.general.siteSettings')}</Title>

        {/* 网站名称设置 */}
        <Form.Item
          name="siteName"
          label={
            <Space>
              <FontSizeOutlined />
              {t('settings.general.siteName')}
            </Space>
          }
          rules={[{ required: true, message: t('settings.general.siteNameRequired') }]}
        >
          <Input placeholder={t('settings.general.siteName')} />
        </Form.Item>

        {/* 网站Logo设置 */}
        <Form.Item
          name="siteLogo"
          label={
            <Space>
              <UploadOutlined />
              {t('settings.general.siteLogo')}
            </Space>
          }
        >
          <Upload
            name="logo"
            listType="picture"
            maxCount={1}
            showUploadList={false}
            beforeUpload={(file) => {
              // 检查文件类型
              const isImage = file.type.startsWith('image/');
              if (!isImage) {
                message.error(t('common.fileTypeError', { type: 'PNG, JPG, JPEG' }));
                return Upload.LIST_IGNORE;
              }

              // 检查文件大小，限制为1MB
              const isLt1M = file.size / 1024 / 1024 < 1;
              if (!isLt1M) {
                message.error(t('common.fileSizeError', { size: '1MB' }));
                return Upload.LIST_IGNORE;
              }

              return true;
            }}
            onChange={handleLogoChange}
            customRequest={({ onSuccess }) => {
              setTimeout(() => {
                onSuccess?.("ok");
              }, 0);
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Button icon={<UploadOutlined />}>{t('settings.general.uploadLogo')}</Button>
              {logoUrl && <img
                src={logoUrl}
                alt="Logo"
                style={{ marginLeft: 16, height: 40 }}
                onError={(e) => {
                  console.error('Logo加载失败，尝试修复路径', e);
                  // 如果加载失败且路径是相对路径，尝试转换为绝对路径
                  const imgElement = e.target as HTMLImageElement;
                  if (!imgElement.src.startsWith('http') && !imgElement.src.startsWith('data:')) {
                    const baseUrl = window.location.origin;
                    const url = imgElement.src.startsWith('/') ? imgElement.src : `/${imgElement.src}`;
                    imgElement.src = `${baseUrl}${url}`;
                  }
                }}
              />}
            </div>
          </Upload>
        </Form.Item>

        <Divider />
        <Title level={5}>{t('settings.general.displaySettings')}</Title>

        {/* 主题设置 */}
        <Form.Item
          name="theme"
          label={
            <Space>
              <BgColorsOutlined />
              {t('settings.general.theme')}
            </Space>
          }
        >
          <Radio.Group onChange={(e) => {
            // 立即更新主题设置
            const newTheme = e.target.value;
            dispatch(setTheme(newTheme));
            form.setFieldsValue({ theme: newTheme });
            message.success(t('settings.general.themeChanged'));
          }}>
            {themeOptions.map(option => (
              <Radio key={option.value} value={option.value}>{option.label}</Radio>
            ))}
          </Radio.Group>
        </Form.Item>

        <Divider />
        <Title level={5}>{t('settings.general.regionSettings')}</Title>

        {/* 货币设置 */}
        <Form.Item
          name="currency"
          label={
            <Space>
              <DollarOutlined />
              {t('settings.general.currency')}
            </Space>
          }
          rules={[{ required: true, message: t('common.required') }]}
        >
          <Select>
            {currencyOptions.map(option => (
              <Option key={option.value} value={option.value}>{option.label}</Option>
            ))}
          </Select>
        </Form.Item>

        {/* 时区设置 */}
        <Form.Item
          name="timezone"
          label={
            <Space>
              <ClockCircleOutlined />
              {t('settings.general.timezone')}
            </Space>
          }
          rules={[{ required: true, message: t('common.required') }]}
        >
          <Select>
            {timezoneOptions.map(option => (
              <Option key={option.value} value={option.value}>{option.label}</Option>
            ))}
          </Select>
        </Form.Item>

        {/* 表单按钮 */}
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={isLoading}>
              {t('common.save')}
            </Button>
            <Button onClick={handleReset}>
              {t('common.reset')}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default GeneralSettingsPanel;
