import React from 'react';
import { Card, Descriptions, Typography, Space, Divider, Button, Tag } from 'antd';
import { InfoCircleOutlined, GithubOutlined, MailOutlined, GlobalOutlined, TeamOutlined, BookOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import VersionControl from './VersionControl';

const { Title, Text, Paragraph } = Typography;

/**
 * 关于面板组件
 */
const AboutPanel: React.FC = () => {
  const { t } = useTranslation();

  // 应用信息
  const appInfo = {
    name: 'PV+Storage Project Economic Analysis',
    version: '1.3.3',
    releaseDate: '2023-12-01',
    developer: '<EMAIL>',
    website: 'pv-analysis.top',
    email: '<EMAIL>'
  };

  // 技术栈信息
  const techStack = [
    { name: 'React', version: '19.1.0' },
    { name: 'TypeScript', version: '5.8.3' },
    { name: 'Vite', version: '6.3.5' },
    { name: 'Ant Design', version: '5.24.9' },
    { name: 'Redux Toolkit', version: '2.7.0' },
    { name: 'React Router', version: '7.5.3' },
    { name: 'i18next', version: '25.1.1' },
    { name: 'ECharts', version: '5.6.0' },
    { name: 'Axios', version: '1.9.0' },
    { name: 'jsPDF', version: '3.0.1' },
    { name: 'html2canvas', version: '1.4.1' },
    { name: 'Express', version: '4.18.2' },
    { name: 'Node.js', version: '18+' }
  ];

  return (
    <Card
      title={
        <Space>
          <InfoCircleOutlined />
          {t('settings.about.title')}
        </Space>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ textAlign: 'center', marginBottom: '24px' }}>
          <Title level={2}>{appInfo.name}</Title>
          <Text type="secondary">{t('settings.about.subtitle')}</Text>
        </div>

        <Descriptions
          title={t('settings.about.appInfo')}
          bordered
          column={{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }}
        >
          <Descriptions.Item label={t('settings.about.version')}>
            <Space>
              <Tag color="blue">{appInfo.version}</Tag>
              <Text type="secondary">{`(${appInfo.releaseDate})`}</Text>
            </Space>
          </Descriptions.Item>

          <Descriptions.Item label={t('settings.about.developer')}>
            <Space>
              <TeamOutlined />
              {appInfo.developer}
            </Space>
          </Descriptions.Item>

          <Descriptions.Item label={t('settings.about.website')}>
            <Button
              type="link"
              icon={<GlobalOutlined />}
              href={`https://${appInfo.website}`}
              target="_blank"
              style={{ padding: 0 }}
            >
              {appInfo.website}
            </Button>
          </Descriptions.Item>

          <Descriptions.Item label={t('settings.about.contact')}>
            <Button
              type="link"
              icon={<MailOutlined />}
              href={`mailto:${appInfo.email}`}
              style={{ padding: 0 }}
            >
              {appInfo.email}
            </Button>
          </Descriptions.Item>
        </Descriptions>

        <Divider />

        <Title level={5}>{t('settings.about.techStack')}</Title>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
          {techStack.map((tech) => (
            <Tag key={tech.name} color="blue">
              {tech.name} {tech.version}
            </Tag>
          ))}
        </div>

        <Divider />

        <Title level={5}>{t('settings.about.description')}</Title>
        <Paragraph>
          {t('settings.about.descriptionContent')}
        </Paragraph>

        <Divider />

        <Title level={5}>{t('settings.about.acknowledgements')}</Title>
        <Paragraph>
          {t('settings.about.acknowledgementsContent')}
        </Paragraph>

        <Divider />

        {/* 版本控制组件 */}
        <VersionControl />

        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <Text type="secondary">
            &copy; {new Date().getFullYear()} {appInfo.developer}. {t('settings.about.copyright')}
          </Text>
        </div>
      </Space>
    </Card>
  );
};

export default AboutPanel;
