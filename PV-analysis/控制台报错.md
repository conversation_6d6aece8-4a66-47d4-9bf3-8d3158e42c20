StorageAnalysisTab.tsx:48 StorageAnalysisTab - 开始渲染，项目ID: 23ec59c2-1d13-4455-bc0f-0ea60581bea9
StorageAnalysisTab.tsx:49 StorageAnalysisTab - 储能设备数量: 1
StorageAnalysisTab.tsx:53 StorageAnalysisTab - 小时数据点数量: 8760
StorageAnalysisTab.tsx:492 StorageAnalysisTab - 开始计算储能统计数据
StorageAnalysisTab.tsx:494 StorageAnalysisTab - 用于计算的小时数据点数量: 8760
StorageAnalysisTab.tsx:525 StorageAnalysisTab - 小时1/1 0时，储能容量: 0 kWh
StorageAnalysisTab.tsx:525 StorageAnalysisTab - 小时1/1 1时，储能容量: 0 kWh
StorageAnalysisTab.tsx:525 StorageAnalysisTab - 小时1/1 2时，储能容量: 0 kWh
StorageAnalysisTab.tsx:510 StorageAnalysisTab - 小时1/1 10时，充电量: 1.772 kWh
StorageAnalysisTab.tsx:510 StorageAnalysisTab - 小时1/1 11时，充电量: 2.078 kWh
StorageAnalysisTab.tsx:519 StorageAnalysisTab - 小时1/1 12时，放电量: 0.034 kWh
StorageAnalysisTab.tsx:510 StorageAnalysisTab - 小时1/1 13时，充电量: 0.063 kWh
StorageAnalysisTab.tsx:519 StorageAnalysisTab - 小时1/1 15时，放电量: 0.339 kWh
StorageAnalysisTab.tsx:519 StorageAnalysisTab - 小时1/1 16时，放电量: 0.968 kWh
StorageAnalysisTab.tsx:531 StorageAnalysisTab - 处理完成的小时数: 8760
StorageAnalysisTab.tsx:532 StorageAnalysisTab - 充电小时数: 2048
StorageAnalysisTab.tsx:533 StorageAnalysisTab - 放电小时数: 3192
StorageAnalysisTab.tsx:534 StorageAnalysisTab - 总充电量: 5849.430000000005 kWh
StorageAnalysisTab.tsx:535 StorageAnalysisTab - 总放电量: 5058.868000000011 kWh
StorageAnalysisTab.tsx:540 StorageAnalysisTab - 储能效率计算: 5058.868000000011 / 5849.430000000005 *100 = 86.48480279275087 %
StorageAnalysisTab.tsx:571 StorageAnalysisTab - 储能收益计算（使用实际电价累计）: 118036.77179999818 日元
StorageAnalysisTab.tsx:48 StorageAnalysisTab - 开始渲染，项目ID: 23ec59c2-1d13-4455-bc0f-0ea60581bea9
StorageAnalysisTab.tsx:49 StorageAnalysisTab - 储能设备数量: 1
StorageAnalysisTab.tsx:53 StorageAnalysisTab - 小时数据点数量: 8760
StorageAnalysisTab.tsx:492 StorageAnalysisTab - 开始计算储能统计数据
StorageAnalysisTab.tsx:494 StorageAnalysisTab - 用于计算的小时数据点数量: 8760
StorageAnalysisTab.tsx:525 StorageAnalysisTab - 小时1/1 0时，储能容量: 0 kWh
StorageAnalysisTab.tsx:525 StorageAnalysisTab - 小时1/1 1时，储能容量: 0 kWh
StorageAnalysisTab.tsx:525 StorageAnalysisTab - 小时1/1 2时，储能容量: 0 kWh
StorageAnalysisTab.tsx:510 StorageAnalysisTab - 小时1/1 10时，充电量: 1.772 kWh
StorageAnalysisTab.tsx:510 StorageAnalysisTab - 小时1/1 11时，充电量: 2.078 kWh
StorageAnalysisTab.tsx:519 StorageAnalysisTab - 小时1/1 12时，放电量: 0.034 kWh
StorageAnalysisTab.tsx:510 StorageAnalysisTab - 小时1/1 13时，充电量: 0.063 kWh
StorageAnalysisTab.tsx:519 StorageAnalysisTab - 小时1/1 15时，放电量: 0.339 kWh
StorageAnalysisTab.tsx:519 StorageAnalysisTab - 小时1/1 16时，放电量: 0.968 kWh
StorageAnalysisTab.tsx:531 StorageAnalysisTab - 处理完成的小时数: 8760
StorageAnalysisTab.tsx:532 StorageAnalysisTab - 充电小时数: 2048
StorageAnalysisTab.tsx:533 StorageAnalysisTab - 放电小时数: 3192
StorageAnalysisTab.tsx:534 StorageAnalysisTab - 总充电量: 5849.430000000005 kWh
StorageAnalysisTab.tsx:535 StorageAnalysisTab - 总放电量: 5058.868000000011 kWh
StorageAnalysisTab.tsx:540 StorageAnalysisTab - 储能效率计算: 5058.868000000011 / 5849.430000000005 *100 = 86.48480279275087 %
StorageAnalysisTab.tsx:571 StorageAnalysisTab - 储能收益计算（使用实际电价累计）: 118036.77179999818 日元
StorageAnalysisChart.tsx:120 StorageAnalysisChart - 开始生成图表，视图模式: hourly
StorageAnalysisChart.tsx:121 StorageAnalysisChart - 选择的指标: (3) ['storageCharge', 'storageDischarge', 'storageCapacity']
StorageDeviceModal.tsx:67 StorageDeviceModal: Redux store中已有设备数据，数据条数: 3
warning.js:30 Warning: [antd: compatible] antd v5 support React is 16 ~ 18. see https://u.ant.design/v5-for-19 for compatible.
warning @ warning.js:30
call @ warning.js:51
warningOnce @ warning.js:58
warning2 @ warning.js:13
defaultReactRender @ UnstableContext.js:13
showWaveEffect @ WaveEffect.js:140
（匿名） @ useWave.js:23
（匿名） @ useEvent.js:10
（匿名） @ useWave.js:36
callRef @ raf.js:30
（匿名） @ raf.js:34
requestAnimationFrame
raf3 @ raf.js:9
callRef @ raf.js:33
wrapperRaf2 @ raf.js:41
showDebounceWave @ useWave.js:35
onClick @ index.js:38
StorageAnalysisTab.tsx:224 Warning: [antd: message] Static function can not consume context like dynamic theme. Please use 'App' component instead.
warning @ warning.js:30
call @ warning.js:51
warningOnce @ warning.js:58
warning2 @ warning.js:13
warnContext @ index.js:39
typeOpen @ index.js:193
staticMethods.<computed> @ index.js:240
handleSaveDevice @ StorageAnalysisTab.tsx:224
handleSubmit @ StorageDeviceModal.tsx:135
await in handleSubmit
（匿名） @ button.js:172
executeDispatch @ react-dom-client.development.js:16368
runWithFiberInDEV @ react-dom-client.development.js:1519
processDispatchQueue @ react-dom-client.development.js:16418
（匿名） @ react-dom-client.development.js:17016
batchedUpdates$1 @ react-dom-client.development.js:3262
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16572
dispatchEvent @ react-dom-client.development.js:20658
dispatchDiscreteEvent @ react-dom-client.development.js:20626
StorageAnalysisTab.tsx:48 StorageAnalysisTab - 开始渲染，项目ID: 23ec59c2-1d13-4455-bc0f-0ea60581bea9
StorageAnalysisTab.tsx:49 StorageAnalysisTab - 储能设备数量: 1
StorageAnalysisTab.tsx:53 StorageAnalysisTab - 小时数据点数量: 8760
StorageAnalysisTab.tsx:492 StorageAnalysisTab - 开始计算储能统计数据
StorageAnalysisTab.tsx:494 StorageAnalysisTab - 用于计算的小时数据点数量: 8760
StorageAnalysisTab.tsx:525 StorageAnalysisTab - 小时1/1 0时，储能容量: 0 kWh
StorageAnalysisTab.tsx:525 StorageAnalysisTab - 小时1/1 1时，储能容量: 0 kWh
StorageAnalysisTab.tsx:525 StorageAnalysisTab - 小时1/1 2时，储能容量: 0 kWh
StorageAnalysisTab.tsx:510 StorageAnalysisTab - 小时1/1 10时，充电量: 1.772 kWh
StorageAnalysisTab.tsx:510 StorageAnalysisTab - 小时1/1 11时，充电量: 2.078 kWh
StorageAnalysisTab.tsx:519 StorageAnalysisTab - 小时1/1 12时，放电量: 0.034 kWh
StorageAnalysisTab.tsx:510 StorageAnalysisTab - 小时1/1 13时，充电量: 0.063 kWh
StorageAnalysisTab.tsx:519 StorageAnalysisTab - 小时1/1 15时，放电量: 0.339 kWh
StorageAnalysisTab.tsx:519 StorageAnalysisTab - 小时1/1 16时，放电量: 0.968 kWh
StorageAnalysisTab.tsx:531 StorageAnalysisTab - 处理完成的小时数: 8760
StorageAnalysisTab.tsx:532 StorageAnalysisTab - 充电小时数: 2048
StorageAnalysisTab.tsx:533 StorageAnalysisTab - 放电小时数: 3192
StorageAnalysisTab.tsx:534 StorageAnalysisTab - 总充电量: 5849.430000000005 kWh
StorageAnalysisTab.tsx:535 StorageAnalysisTab - 总放电量: 5058.868000000011 kWh
StorageAnalysisTab.tsx:540 StorageAnalysisTab - 储能效率计算: 5058.868000000011 / 5849.430000000005 *100 = 86.48480279275087 %
StorageAnalysisTab.tsx:571 StorageAnalysisTab - 储能收益计算（使用实际电价累计）: 118036.77179999818 日元
StorageAnalysisTab.tsx:48 StorageAnalysisTab - 开始渲染，项目ID: 23ec59c2-1d13-4455-bc0f-0ea60581bea9
StorageAnalysisTab.tsx:49 StorageAnalysisTab - 储能设备数量: 1
StorageAnalysisTab.tsx:53 StorageAnalysisTab - 小时数据点数量: 8760
StorageAnalysisTab.tsx:492 StorageAnalysisTab - 开始计算储能统计数据
StorageAnalysisTab.tsx:494 StorageAnalysisTab - 用于计算的小时数据点数量: 8760
StorageAnalysisTab.tsx:525 StorageAnalysisTab - 小时1/1 0时，储能容量: 0 kWh
StorageAnalysisTab.tsx:525 StorageAnalysisTab - 小时1/1 1时，储能容量: 0 kWh
StorageAnalysisTab.tsx:525 StorageAnalysisTab - 小时1/1 2时，储能容量: 0 kWh
StorageAnalysisTab.tsx:510 StorageAnalysisTab - 小时1/1 10时，充电量: 1.772 kWh
StorageAnalysisTab.tsx:510 StorageAnalysisTab - 小时1/1 11时，充电量: 2.078 kWh
StorageAnalysisTab.tsx:519 StorageAnalysisTab - 小时1/1 12时，放电量: 0.034 kWh
StorageAnalysisTab.tsx:510 StorageAnalysisTab - 小时1/1 13时，充电量: 0.063 kWh
StorageAnalysisTab.tsx:519 StorageAnalysisTab - 小时1/1 15时，放电量: 0.339 kWh
StorageAnalysisTab.tsx:519 StorageAnalysisTab - 小时1/1 16时，放电量: 0.968 kWh
StorageAnalysisTab.tsx:531 StorageAnalysisTab - 处理完成的小时数: 8760
StorageAnalysisTab.tsx:532 StorageAnalysisTab - 充电小时数: 2048
StorageAnalysisTab.tsx:533 StorageAnalysisTab - 放电小时数: 3192
StorageAnalysisTab.tsx:534 StorageAnalysisTab - 总充电量: 5849.430000000005 kWh
StorageAnalysisTab.tsx:535 StorageAnalysisTab - 总放电量: 5058.868000000011 kWh
StorageAnalysisTab.tsx:540 StorageAnalysisTab - 储能效率计算: 5058.868000000011 / 5849.430000000005 *100 = 86.48480279275087 %
StorageAnalysisTab.tsx:571 StorageAnalysisTab - 储能收益计算（使用实际电价累计）: 118036.77179999818 日元
StorageAnalysisChart.tsx:120 StorageAnalysisChart - 开始生成图表，视图模式: hourly
StorageAnalysisChart.tsx:121 StorageAnalysisChart - 选择的指标: (3) ['storageCharge', 'storageDischarge', 'storageCapacity']
BasicInfoStep.tsx:144 Warning: Instance created by `useForm` is not connected to any Form element. Forget to pass `form` prop?
warning @ warning.js:30
call @ warning.js:51
warningOnce @ warning.js:58
（匿名） @ useForm.js:160
setTimeout
（匿名） @ useForm.js:157
（匿名） @ useForm.js:205
getIncompleteFields @ BasicInfoStep.tsx:144
BasicInfoStep @ BasicInfoStep.tsx:164
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooks @ react-dom-client.development.js:5529
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1519
performUnitOfWork @ react-dom-client.development.js:15132
workLoopConcurrentByScheduler @ react-dom-client.development.js:15126
renderRootConcurrent @ react-dom-client.development.js:15101
performWorkOnRoot @ react-dom-client.development.js:14418
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<BasicInfoStep>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
NewProject @ NewProject.tsx:178
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1519
performUnitOfWork @ react-dom-client.development.js:15132
workLoopConcurrentByScheduler @ react-dom-client.development.js:15126
renderRootConcurrent @ react-dom-client.development.js:15101
performWorkOnRoot @ react-dom-client.development.js:14418
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<NewProject>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
EditProject @ EditProject.tsx:40
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1519
performUnitOfWork @ react-dom-client.development.js:15132
workLoopConcurrentByScheduler @ react-dom-client.development.js:15126
renderRootConcurrent @ react-dom-client.development.js:15101
performWorkOnRoot @ react-dom-client.development.js:14418
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
projectDataService.ts:138 项目有电力使用数据，计算8760个小时的用电量
projectDataService.ts:175 更新项目电力使用数据: sameEveryday
projectDataService.ts:213 应用电力使用数据到小时数据中
projectDataService.ts:221 应用电力使用数据时出错: TypeError: Cannot assign to read only property 'electricityConsumption' of object '#<Object>'
    at applyElectricityUsageToHourlyData (electricityUsageCalculator.ts:204:21)
    at updateElectricityUsage (projectDataService.ts:216:46)
    at initializeProjectData (projectDataService.ts:139:5)
    at NewProject.tsx:283:31
    at react-stack-bottom-frame (react-dom-client.development.js:23949:20)
    at runWithFiberInDEV (react-dom-client.development.js:1519:30)
    at commitHookEffectListMount (react-dom-client.development.js:11905:29)
    at commitHookPassiveMountEffects (react-dom-client.development.js:12026:11)
    at commitPassiveMountOnFiber (react-dom-client.development.js:13841:13)
    at recursivelyTraversePassiveMountEffects (react-dom-client.development.js:13815:11)
updateElectricityUsage @ projectDataService.ts:221
initializeProjectData @ projectDataService.ts:139
（匿名） @ NewProject.tsx:283
react-stack-bottom-frame @ react-dom-client.development.js:23949
runWithFiberInDEV @ react-dom-client.development.js:1519
commitHookEffectListMount @ react-dom-client.development.js:11905
commitHookPassiveMountEffects @ react-dom-client.development.js:12026
commitPassiveMountOnFiber @ react-dom-client.development.js:13841
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13844
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13834
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13957
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13815
commitPassiveMountOnFiber @ react-dom-client.development.js:13853
flushPassiveEffects @ react-dom-client.development.js:15737
flushPendingEffects @ react-dom-client.development.js:15702
performSyncWorkOnRoot @ react-dom-client.development.js:16228
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16079
flushSpawnedWork @ react-dom-client.development.js:15677
commitRoot @ react-dom-client.development.js:15403
commitRootWhenReady @ react-dom-client.development.js:14652
performWorkOnRoot @ react-dom-client.development.js:14575
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<NewProject>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
EditProject @ EditProject.tsx:40
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1519
performUnitOfWork @ react-dom-client.development.js:15132
workLoopConcurrentByScheduler @ react-dom-client.development.js:15126
renderRootConcurrent @ react-dom-client.development.js:15101
performWorkOnRoot @ react-dom-client.development.js:14418
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
electricityUsageCalculator.ts:230 应用电力使用数据完成，第一个小时点的用电量: 1
NewProject.tsx:285 项目详细数据已初始化: {id: '23ec59c2-1d13-4455-bc0f-0ea60581bea9', name: '高总家', location: '', capacity: 15, status: 'completed', …}
NewProject.tsx:291 初始化时检查项目数据，更新步骤验证状态
NewProject.tsx:305 基本信息验证结果: true
NewProject.tsx:308 基本信息有效，但状态未更新，现在更新状态
projectDataService.ts:138 项目有电力使用数据，计算8760个小时的用电量
projectDataService.ts:175 更新项目电力使用数据: sameEveryday
projectDataService.ts:213 应用电力使用数据到小时数据中
projectDataService.ts:221 应用电力使用数据时出错: TypeError: Cannot assign to read only property 'electricityConsumption' of object '#<Object>'
    at applyElectricityUsageToHourlyData (electricityUsageCalculator.ts:204:21)
    at updateElectricityUsage (projectDataService.ts:216:46)
    at initializeProjectData (projectDataService.ts:139:5)
    at NewProject.tsx:283:31
    at react-stack-bottom-frame (react-dom-client.development.js:23949:20)
    at runWithFiberInDEV (react-dom-client.development.js:1519:30)
    at commitHookEffectListMount (react-dom-client.development.js:11905:29)
    at commitHookPassiveMountEffects (react-dom-client.development.js:12026:11)
    at reconnectPassiveEffects (react-dom-client.development.js:14004:11)
    at recursivelyTraverseReconnectPassiveEffects (react-dom-client.development.js:13976:9)
updateElectricityUsage @ projectDataService.ts:221
initializeProjectData @ projectDataService.ts:139
（匿名） @ NewProject.tsx:283
react-stack-bottom-frame @ react-dom-client.development.js:23949
runWithFiberInDEV @ react-dom-client.development.js:1519
commitHookEffectListMount @ react-dom-client.development.js:11905
commitHookPassiveMountEffects @ react-dom-client.development.js:12026
reconnectPassiveEffects @ react-dom-client.development.js:14004
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:13976
reconnectPassiveEffects @ react-dom-client.development.js:13997
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:15968
runWithFiberInDEV @ react-dom-client.development.js:1519
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15928
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15935
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:15977
flushPassiveEffects @ react-dom-client.development.js:15747
flushPendingEffects @ react-dom-client.development.js:15702
performSyncWorkOnRoot @ react-dom-client.development.js:16228
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16079
flushSpawnedWork @ react-dom-client.development.js:15677
commitRoot @ react-dom-client.development.js:15403
commitRootWhenReady @ react-dom-client.development.js:14652
performWorkOnRoot @ react-dom-client.development.js:14575
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<NewProject>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
EditProject @ EditProject.tsx:40
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1519
performUnitOfWork @ react-dom-client.development.js:15132
workLoopConcurrentByScheduler @ react-dom-client.development.js:15126
renderRootConcurrent @ react-dom-client.development.js:15101
performWorkOnRoot @ react-dom-client.development.js:14418
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
electricityUsageCalculator.ts:230 应用电力使用数据完成，第一个小时点的用电量: 1
NewProject.tsx:285 项目详细数据已初始化: {id: '23ec59c2-1d13-4455-bc0f-0ea60581bea9', name: '高总家', location: '', capacity: 15, status: 'completed', …}
NewProject.tsx:291 初始化时检查项目数据，更新步骤验证状态
NewProject.tsx:305 基本信息验证结果: true
NewProject.tsx:308 基本信息有效，但状态未更新，现在更新状态
BasicInfoStep.tsx:49 初始化后立即验证表单
BasicInfoStep.tsx:102 开始验证基础信息表单
BasicInfoStep.tsx:106 当前表单值: {regionCode: 'kanto', prefectureCode: 'saitama', name: '高总家', capacity: 15, region: '关东地方', …}
BasicInfoStep.tsx:115 缺失字段: []
BasicInfoStep.tsx:118 表单验证通过
NewProject.tsx:359 步骤验证 - 步骤: basicInfo, 验证结果: true
BasicInfoStep.tsx:49 初始化后立即验证表单
BasicInfoStep.tsx:102 开始验证基础信息表单
BasicInfoStep.tsx:106 当前表单值: {regionCode: 'kanto', prefectureCode: 'saitama', name: '高总家', capacity: 15, region: '关东地方', …}
BasicInfoStep.tsx:115 缺失字段: []
BasicInfoStep.tsx:118 表单验证通过
NewProject.tsx:359 步骤验证 - 步骤: basicInfo, 验证结果: true
