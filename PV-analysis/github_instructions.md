# GitHub 推送指南

## 准备工作

您的项目已经初始化为Git仓库，并且已经提交了初始代码。现在您需要将代码推送到GitHub。

## 步骤1：创建GitHub仓库

1. 访问 https://github.com/new
2. 仓库名称：`pv-analysis`
3. 描述：`光伏+储能项目经济性分析系统`
4. 选择公开或私有
5. 不要初始化README
6. 点击"Create repository"

## 步骤2：推送代码到GitHub

在终端中执行以下命令：

```bash
# 进入项目目录
cd /Users/<USER>/Documents/augment-projects/PV

# 添加远程仓库
git remote add origin https://github.com/wsd07/pv-analysis.git

# 设置主分支名称
git branch -M main

# 推送代码到GitHub
git push -u origin main
```

执行最后一个命令时，系统会要求您输入GitHub用户名和密码。如果您启用了双因素认证，则需要使用个人访问令牌而不是密码。

## 使用个人访问令牌（如果需要）

如果您启用了双因素认证，请按照以下步骤创建个人访问令牌：

1. 访问 https://github.com/settings/tokens
2. 点击"Generate new token"
3. 为令牌提供一个描述性名称，如"PV项目推送"
4. 选择"repo"范围（这将授予对仓库的完全访问权限）
5. 点击"Generate token"
6. 复制生成的令牌

然后，在执行`git push`命令时，使用您的GitHub用户名和个人访问令牌作为密码。

## 使用SSH密钥（推荐）

如果您想避免每次都输入用户名和密码，可以设置SSH密钥：

1. 检查是否已有SSH密钥：
   ```bash
   ls -la ~/.ssh
   ```

2. 如果没有，生成新的SSH密钥：
   ```bash
   ssh-keygen -t ed25519 -C "<EMAIL>"
   ```

3. 将SSH密钥添加到ssh-agent：
   ```bash
   eval "$(ssh-agent -s)"
   ssh-add ~/.ssh/id_ed25519
   ```

4. 复制SSH公钥：
   ```bash
   pbcopy < ~/.ssh/id_ed25519.pub
   ```

5. 访问 https://github.com/settings/keys
6. 点击"New SSH key"
7. 为密钥提供一个标题，如"MacBook Pro"
8. 粘贴公钥
9. 点击"Add SSH key"

10. 更新远程仓库URL为SSH格式：
    ```bash
    git remote set-<NAME_EMAIL>:wsd07/pv-analysis.git
    ```

11. 推送代码：
    ```bash
    git push -u origin main
    ```

## 验证推送是否成功

推送完成后，访问 https://github.com/wsd07/pv-analysis 查看您的代码是否已成功上传。

## 后续更新

每次修改代码后，您可以使用以下命令将更改推送到GitHub：

```bash
git add .
git commit -m "更新说明"
git push
```
