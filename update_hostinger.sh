#!/bin/bash

# Hostinger服务器更新脚本
# 光伏+储能项目经济性分析系统

set -e  # 遇到错误立即退出

# 配置变量
SERVER_HOST="*************"
SERVER_PORT="65002"
SERVER_USER="u387728176"

# 颜色输出函数
print_info() {
    echo -e "\033[34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[32m[SUCCESS]\033[0m $1"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

# 更新服务器代码
update_server() {
    print_info "开始更新Hostinger服务器代码..."

    ssh -i ~/.ssh/hostinger_key -p $SERVER_PORT $SERVER_USER@$SERVER_HOST << 'ENDSSH'
        set -e

        # 设置颜色输出函数
        print_info() {
            echo -e "\033[34m[INFO]\033[0m $1"
        }

        print_success() {
            echo -e "\033[32m[SUCCESS]\033[0m $1"
        }

        # 加载NVM
        export NVM_DIR="$HOME/.nvm"
        [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
        nvm use node

        # 进入项目目录
        cd ~/domains/pv-analysis.top/public_html

        # 拉取最新代码
        print_info "拉取最新代码..."
        git pull origin main

        # 设置环境变量
        print_info "设置环境变量..."
        echo "VITE_API_BASE_URL=/api" > .env.production

        # 构建前端应用
        print_info "构建前端应用..."
        # 备份原始package.json
        cp package.json package.json.bak
        # 修改构建命令
        sed -i 's/"build": "tsc -b && vite build"/"build": "vite build"/' package.json
        # 构建应用
        npm run build
        # 恢复原始package.json
        mv package.json.bak package.json

        # 重启API服务器
        print_info "重启API服务器..."
        cd server

        # 停止现有服务器进程
        if [ -f ".server.pid" ]; then
            SERVER_PID=\$(cat .server.pid)
            if ps -p \$SERVER_PID > /dev/null 2>&1; then
                kill \$SERVER_PID
                sleep 2
                if ps -p \$SERVER_PID > /dev/null 2>&1; then
                    kill -9 \$SERVER_PID
                fi
            fi
            rm -f .server.pid
        fi

        # 清理端口占用
        pkill -f "node.*src/index.js" || true
        sleep 2

        # 尝试使用PM2重启
        if command -v pm2 &> /dev/null; then
            print_info "使用PM2重启服务器..."
            pm2 delete pv-server 2>/dev/null || true
            pm2 start src/index.js --name pv-server --log server.log --error server.error.log
        else
            print_info "使用nohup启动服务器..."
            nohup npm start > server.log 2>&1 &
            SERVER_PID=\$!
            echo \$SERVER_PID > .server.pid
            print_info "服务器已启动，PID: \$SERVER_PID"
        fi

        # 等待服务器启动
        sleep 5

        # 检查服务器是否正常运行
        for i in {1..10}; do
            if curl -s http://localhost:3001/api/health > /dev/null; then
                print_success "服务器重启成功"
                break
            else
                print_warning "等待服务器启动... (\$i/10)"
                sleep 2
            fi
        done

        cd ..

        # 复制构建文件到根目录
        print_info "更新前端文件..."
        cp -r dist/* .

        print_success "更新完成！"

        # 显示服务器状态
        print_info "检查服务器状态..."
        if command -v pm2 &> /dev/null; then
            pm2 list
        else
            if [ -f "server/.server.pid" ]; then
                SERVER_PID=\$(cat server/.server.pid)
                if ps -p \$SERVER_PID > /dev/null 2>&1; then
                    print_success "服务器正在运行，PID: \$SERVER_PID"
                else
                    print_error "服务器进程未运行"
                fi
            else
                print_warning "未找到服务器PID文件"
            fi
        fi

        # 最终健康检查
        print_info "执行最终健康检查..."
        if curl -s http://localhost:3001/api/health > /dev/null; then
            print_success "API服务器健康检查通过"
        else
            print_error "API服务器健康检查失败"
        fi

ENDSSH
}

# 主函数
main() {
    echo "=================================================="
    echo "  光伏+储能项目经济性分析系统 - Hostinger更新脚本"
    echo "=================================================="
    echo ""

    update_server

    echo ""
    echo "=================================================="
    echo "  更新完成！"
    echo "=================================================="
    echo "  网站地址: https://pv-analysis.top"
    echo "  API地址: https://pv-analysis.top/api"
    echo "=================================================="
}

# 执行主函数
main "$@"
